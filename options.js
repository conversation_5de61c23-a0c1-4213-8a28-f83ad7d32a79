document.addEventListener("DOMContentLoaded", () => {
    const websocketHostInput = document.getElementById("websocketHost");
    const websocketPortInput = document.getElementById("websocketPort");
    const windowsSessionInput = document.getElementById("windowsSessionName");
    const logLevelSelect = document.getElementById("logLevel");
    const saveButton = document.getElementById("save");
    const resetButton = document.getElementById("reset");
    const messageDiv = document.getElementById("message");

    const defaultSettings = {
        websocketHost: "localhost",
        websocketPort: "8976",
        windowsSessionName: "",
        logLevel: "information"
    };

    function loadSettings() {
        chrome.storage.local.get(["websocketHost", "websocketPort", "windowsSessionName", "logLevel"], (data) => {
            websocketHostInput.value = data.websocketHost || defaultSettings.websocketHost;
            websocketPortInput.value = data.websocketPort || defaultSettings.websocketPort;
            windowsSessionInput.value = data.windowsSessionName || defaultSettings.windowsSessionName;
            logLevelSelect.value = data.logLevel || defaultSettings.logLevel;
        });
    }

    function showMessage(text, type = "success") {
        messageDiv.textContent = text;
        messageDiv.className = type;
        messageDiv.style.display = "block";
        setTimeout(() => {
            messageDiv.style.display = "none";
        }, 6000);
    }

    saveButton.addEventListener("click", () => {
        const settings = {
            websocketHost: websocketHostInput.value.trim(),
            websocketPort: websocketPortInput.value.trim(),
            windowsSessionName: windowsSessionInput.value.trim(),
            logLevel: logLevelSelect.value.trim()
        };

        chrome.storage.local.set(settings, () => {
            showMessage("Settings saved successfully! Please restart your browser to apply these changes.", "success");
        });
    });

    resetButton.addEventListener("click", () => {
        chrome.storage.local.set(defaultSettings, () => {
            loadSettings();
            showMessage("Settings reset to default! Please restart your browser to apply these changes.", "success");
        });
    });

    loadSettings();
});
