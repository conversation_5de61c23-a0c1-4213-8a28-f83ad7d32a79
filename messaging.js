const KEEP_ALIVE_TIME = 30 * 1000;

export function iLog(...args) {
  console.log(`IRIS_WORKER :: ${args.join(" ")}`);
}

export class IRISException extends Error {
  constructor(typeException, message) {
    super(message);
    this.typeException = typeException;
  }
}

// Class de communication de base
class CommunicationFromIrisToExtension {
  constructor() {
    if (new.target === CommunicationFromIrisToExtension) {
      throw new Error("Cannot instantiate abstract class");
    }
    this.callbackOnDisconnect = null;
    this.callbackOnReceiveMessage = null;
  }

  async connect() {
    throw new Error("Method 'connect' must be implemented");
  }

  isConnected() {
    throw new Error("Method 'isConnected' must be implemented");
  }

  disconnect() {
    throw new Error("Method 'disconnect' must be implemented");
  }

  getWindowsSessionNameUniforme()
  {
    throw new Error("Method 'getWindowsSessionNameUniforme' must be implemented");
  }

  OnDisconnect(callback) {
    this.callbackOnDisconnect = callback;
  }

  onReceiveMessage(callback) {
    this.callbackOnReceiveMessage = callback;
  }

  handleRequest(callback, requestAsString, browserIdentifier) {
    let responseMessage = null;
    let request = null;
    let idMessage = "";
    let destination = "";
    let status = 0;
    let messageException = "";
    let typeException = "";

    try {
      iLog(`Request string  : ${requestAsString}`);

      request = JSON.parse(requestAsString);
     // iLog(`Request  : ${request}`);
      if (
          request.id &&
          request.destination.trim().toUpperCase() === browserIdentifier.toUpperCase() &&
          request.request
      ) {
        callback(request.request)
          .then((responseMessageCallBack) => {
            if (!responseMessageCallBack) {
              responseMessage = null;
            } else {
              responseMessage = responseMessageCallBack;
            }
          })
          .catch((error) => {
            status = 1;
            messageException = error.message;
            if (error instanceof IRISException) {
              typeException = error.typeException;
            } else {
              typeException =
                "TechnicalIrisException:" + error.constructor.name;
            }
          })
          .finally(() => {
            if (request !== null) {
              idMessage = request.id;
              destination = request.source;
            }
            const response = {
              id: idMessage,
              request: null,
              response: responseMessage,
              source: browserIdentifier,
              destination: destination,
              status: status,
              typeException: typeException,
              messageException: messageException,
            };

              this.sendResponseMessage(JSON.stringify(response));
              iLog("Response  : " + JSON.stringify(response));
            });
      } else {
        iLog(
            "The request must have an ID, request message and the destination must be '"+browserIdentifier+"'"
        )
        return;
      }
    } catch (error) {
      iLog(`Error : ${error.message} \nStacktrace: ${error.stack}`);
    }
  }
}

// Class pour la communication via WebSocket
class WebSocketCommunicationFromIrisToExtension extends CommunicationFromIrisToExtension {
  constructor(modeConnexion) {
    super();
    this.webSocket = null;
    this.delayBeforeReload = 5000;
    this.windowsSessionName = "";
    this.modeConnexion = modeConnexion;
    this.browserIdentifier = this.getBrowserModeConnexion();
  }


  async getWebSocketUrl() {
    return new Promise((resolve, reject) => {
        chrome.storage.local.get(["websocketHost", "websocketPort", "windowsSessionName"], (data) => {
            
            let websocketProtocol = "ws://";
            let webSocketHost = data.websocketUrl || "localhost"; // Valeur par défaut si non définie "ws://localhost:8976"
            let websocketPort = data.websocketPort || "8976"; // Valeur par défaut si non définie 
            
            this.windowsSessionName = data.windowsSessionName || "";

            this.browserIdentifier = this.getBrowserModeConnexion();
              
            this.browserIdentifier += this.getWindowsSessionNameUniforme();


            if (webSocketHost.startsWith(websocketProtocol)) {
              // Pas besoin d'ajouter le protocole ws:// deux fois.
              websocketProtocol = "";
          }

            let webSocketUrl = `${websocketProtocol}${webSocketHost}:${websocketPort}/chat?name=${this.browserIdentifier}`; //exemple ws://localhost:8976/chat?name=ChromeExtension_Me
            resolve(webSocketUrl);
        });
    });
  }

  getWindowsSessionNameUniforme() 
  {
    if (typeof this.windowsSessionName !== "string" || !this.windowsSessionName.trim()) {
      return "";
    }
    return  `_${this.windowsSessionName.trim().replace(/\s+/g, "_")}`;
  }

  getBrowserModeConnexion()
  {
    return "Chrome"+this.modeConnexion;
  }

  
  async connect() {
    
    let urlWebSocket = await this.getWebSocketUrl();
        
    iLog(`Connecting to IRIS WebSocket : ${urlWebSocket}`);

    this.webSocket = new WebSocket(urlWebSocket);

    // Set a timeout to check the WebSocket's readyState
    const timeout = 10000; // Adjust this value as needed

    const connectTimeout = setTimeout(() => {
      if (this.webSocket.readyState !== WebSocket.OPEN) {
        iLog("WebSocket connection timed out");
        this.reloadExtension();
      }
    }, timeout);

    this.webSocket.onopen = (event) => {
      iLog("Websocket is connected to " + urlWebSocket);
      clearTimeout(connectTimeout);
      if (this.callbackOnReceiveMessage !== null) {
        this.onReceiveMessage(this.callbackOnReceiveMessage);
      }
      if (this.callbackOnDisconnect !== null) {
        this.onDisconnect(this.callbackOnDisconnect);
      }
      this.sendPing();
    };

    this.webSocket.onclose = (event) => {
      if (event.code === 1006) {
        // Code 1006: Connection was closed abnormally (e.g., network issues)
        console.log("WebSocket connection closed ", event);

        // Handle the closure here, for example, by reloading the extension
        //this.reloadExtension();
      }
    };
  }

  reloadExtension() {
    console.log("Reloading extension...");
    chrome.runtime.reload();
    // const reloadFunction = () => {
    //   console.log("Reloading extension after delay:", this.delayBeforeReload);
    //   chrome.runtime.reload();
    // };
    // setTimeout(reloadFunction, this.delayBeforeReload);
    // this.delayBeforeReload = Math.min(this.delayBeforeReload * 2, 10 * 60 * 1000);
  }

  isConnected() {
    /*
    Possible status:
    WebSocket.CONNECTING (numeric value 0): The connection is being established.
    WebSocket.OPEN (numeric value 1): The connection is established and ready to communicate.
    WebSocket.CLOSING (numeric value 2): The connection is in the process of closing.
    WebSocket.CLOSED (numeric value 3): The connection is closed or could not be opened.
    */
    const isConnected =
        this.webSocket && this.webSocket.readyState === WebSocket.OPEN;

    if (!isConnected) {
      iLog("The websocket is not connected.");
    }

    return isConnected;
  }
  disconnect() {
    if (this.webSocket === null) {
      return;
    }
    this.webSocket.close();
    this.webSocket = null;
  }

  onDisconnect(callback) {
    super.onDisconnect(callback);
    this.webSocket.onclose = (event) => {
      console.log("websocket connection closed");
      this.webSocket = null;
      if (callback !== null) {
        callback();
      }
    };
  }

  onReceiveMessage(callback) {
    super.onReceiveMessage(callback);
    this.webSocket.onmessage = (event) => {
      if (callback !== null) {
        this.handleRequest(callback, event.data, this.browserIdentifier);
      }
    };
  }

  sendResponseMessage(responseMessage) {
    if (!this.isConnected()) {
      this.connect();
    }

    if (this.webSocket.readyState === WebSocket.OPEN) {
      this.webSocket.send(responseMessage);
    } else {
      console.error("WebSocket connection is not open.");
    }
  }

  sendPing() {
    const sendPingIntervalId = setInterval(() => {
      if (this.isConnected()) {
        this.webSocket.send("ping");
      } else {
        clearInterval(sendPingIntervalId);
        this.connect();
      }
    }, KEEP_ALIVE_TIME);
  }

  sendJsonMessage(message) {
    try {
      if (!this.isConnected()) {
        iLog("WebSocket is not connected. Attempting to reconnect...");
        this.connect();  // Try to reconnect
        return;
      }

      //message.source = this.browserIdentifier;
      const jsonString = JSON.stringify(message);
      if (this.webSocket.readyState === WebSocket.OPEN) {
        this.webSocket.send(jsonString);
        iLog("Sent JSON message:", jsonString);
      } else {
        iLog("WebSocket connection is not open. Message not sent.");
      }
    } catch (error) {
      iLog("Error sending JSON message:", `error: ${error.message}\nStacktrace: ${error.stack}`);
    }
  }
}

// Class pour la communication via Native Messaging
class NativeMessagingCommunicationFromIrisToExtension extends CommunicationFromIrisToExtension {
  constructor() {
    super();
    this.port = null;
    this.onNativeMessage = null;
    this.onDisconnected = null;
  }

  async connect() {
    iLog("Connecting using NativeMessaging ...");
    const hostName = "io.novelis.iris.chromeserver.message";
    this.port = chrome.runtime.connectNative(hostName);
    this.port.onDisconnect.addListener(function () {
      console.log("NativeMessaging is connected to " + hostName);
      if (this.callbackOnReceiveMessage !== null) {
        this.onReceiveMessage(this.callbackOnReceiveMessage);
      }
      if (this.callbackOnDisconnect !== null) {
        this.onDisconnect(this.callbackOnDisconnect);
      }
    });
  }

  isConnected() {
    return this.port;
  }

  disconnect() {
    if (this.port === null) {
      return;
    }
    if (this.onNativeMessage !== null) {
      this.port.onMessage.removeListener(this.onNativeMessage);
    }
    if (this.onDisconnected !== null) {
      this.port.onDisconnect.removeListener(this.onDisconnected);
    }
    this.port = null;
  }

  onDisconnect(callback) {
    super.onDisconnect(callback);
    this.onDisconnected = () => {
      console.log("NativeMessaging connection closed");
      //this.disconnect();
      this.port = null;
      callback();
    };
    this.port.onDisconnect.addListener(this.onDisconnected);
  }

  onReceiveMessage(callback) {
    super.onReceiveMessage(callback);
    this.onNativeMessage = (message) => {
      if (callback !== null) {
        this.handleRequest(callback, message);
      }
    };
    if (!isConnected()) {
      this.connect();
    }
    this.port.onMessage.addListener(this.onNativeMessage);
  }

  sendResponseMessage(responseMessage) {
    if (!isConnected()) {
      this.connect();
    }
    this.port.postMessage(responseMessage);
  }
}

export class CommunicationFactory {
  //modeConnexion : see possible values of ModeConnexion in the worker file
  static createCommunication(type, modeConnexion) {
    switch (type) {
      case "WebSocket":
        return new WebSocketCommunicationFromIrisToExtension(modeConnexion);
      case "NativeMessaging":
        return new NativeMessagingCommunicationFromIrisToExtension();
      default:
        throw new Error("Different communication type ");
    }
  }
}
