import {
  iLog,
  CommunicationFactory,
  IRISException,
} from "./messaging.js";

//Mode de communication possible : WebSocket, NativeMessaging
const communicationType = "WebSocket";
const ModeConnexion = {
  EXTENTION : "Extension",
  IRISKT : "IrisKT"
};

/* Declaration of public variables
 *
 */
let contentScriptReloaded = false;
let isPageReloaded = false
let isKTStarted = false;
let isSpyElementStarted = false;
let KTSocket= null;
let TAB_NOT_INDECATED = 0;
let ID_PRINCIPALe_PAGE = 0;

//Cette liste permet d'executer l'action dans la page principale et pas un sous frame
const actions_use_pricipal_page = [
  "GETDOM",
  "RELOADPAGE",
  "GOBACK<PERSON><PERSON>EVIOUSPAGE",
  "GOTONEXTPAGE",
  "GETPAGEURL",
  "GETPAGETITLE"
];

function createResponseObject() {
  return {
    text: "",
    value: "",
    isExist: false,
    success: false,
    message: "",
    idStep: "",
    pageId: 0,
    timeout: 0,
  };
}

//Paramétrage de l'extension (voir options.js et option.html)
/*chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.set({
      websocketUrl: "ws://localhost:8976",
      windowsSessionName: "",
      logLevel: "Inforomation"
  });
});
*/



const communication = CommunicationFactory.createCommunication(communicationType, ModeConnexion.EXTENTION);

(async () => {

  await communication.connect();

  communication.onReceiveMessage(async (message) => {
    console.log(" ================================ New message received ================================");
    const responseMessage = await onNativeMessage(message);
    return responseMessage;
  });

})();


async function startKT(tabId) {
  console.log("Sta")
  const result = await sendMessageToContentPage(tabId, { action: "STARTKT" });
  if (result?.success) {
    chrome.action.setIcon({
      tabId: tabId,
      path: {
        16: "icons/IRIS_KT_16x16.png",
        32: "icons/IRIS_KT_32x32.png",
        48: "icons/IRIS_KT_48x48.png",
        128: "icons/IRIS_KT_128x128.png"
      }
    });
   /* chrome.action.setTitle({
      tabId: tabId,
      title: "IRIS (KT recording..)"
    });*/
  }
}

async function stopKT(tabId) {
  const result = await sendMessageToContentPage(tabId, { action: "STOPKT" });
  if (result.success) {
    chrome.action.setIcon({
      tabId: tabId,
      path: {
        16: "icons/IRIS_16x16.png",
        32: "icons/IRIS_32x32.png",
        48: "icons/IRIS_48x48.png",
        128: "icons/IRIS_128x128.png"
      }
    });
   /* chrome.action.setTitle({
      tabId: tabId,
      title: "IRIS"
    });*/
  }
}

async function startSpyElment(tabId) {
  const result = await sendMessageToContentPage(tabId, { action: "SPYELEMENT" });
  iLog("result : inside startSpyElment ", result);
  if (result?.success) {
    chrome.action.setIcon({
      tabId: tabId,
      path: {
        16: "icons/IRIS_KT_16x16.png",
        32: "icons/IRIS_KT_32x32.png",
        48: "icons/IRIS_KT_48x48.png",
        128: "icons/IRIS_KT_128x128.png"
      }
    });
  }
}

async function stopSpyElement(tabId) {
  const result = await sendMessageToContentPage(tabId, { action: "STOPSPYELEMENT" });
  iLog("result : inside stopSpyElement ", result);
  if (result.success) {
    chrome.action.setIcon({
      tabId: tabId,
      path: {
        16: "icons/IRIS_16x16.png",
        32: "icons/IRIS_32x32.png",
        48: "icons/IRIS_48x48.png",
        128: "icons/IRIS_128x128.png"
      }
    });
  }
}

function getLastTab() {
  return new Promise(async (resolve, reject) => {
    try {
      const windows = await new Promise((chromeResolve) => {
        chrome.windows.getAll({ populate: true }, (result) => {
          chromeResolve(result);
        });
      });

      let lastTab = null;

      for (const window of windows) {
        for (const tab of window.tabs) {
          iLog("the latest tab is " + tab.id);
          lastTab = tab;
        }
      }

      if (lastTab) {
        resolve(lastTab);
      } else {
        reject(new Error("No tabs found"));
      }
    } catch (error) {
      reject(error);
    }
  });
}

function getAllTabs() {
  return new Promise(async (resolve, reject) => {
    try {
      const windows = await new Promise((chromeResolve) => {
        chrome.windows.getAll({ populate: true }, (result) => {
          chromeResolve(result);
        });
      });

      const allTabs = [];

      for (const window of windows) {
        for (const tab of window.tabs) {
          console.log("Found tab: " + tab.id);
          allTabs.push(tab);
        }
      }

      if (allTabs.length > 0) {
        resolve(allTabs);
      } else {
        reject(new Error("No tabs found"));
      }
    } catch (error) {
      reject(error);
    }
  });
}

function getActiveTabIdAcrossAllWindows() {
  return new Promise((resolve, reject) => {
    try {
      chrome.windows.getAll({ populate: true }, function(windows) {
        for (let window of windows) {
          for (let tab of window.tabs) {
            if (tab.active) {
              resolve(tab.id);
              return;
            }
          }
        }
        reject(new Error("No active tab found"));
      });
    } catch (error) {
      reject(error);
    }
  });
}

function getSelectedTab() {
  return new Promise(async (resolve, reject) => {
    try {
      const tabs = await new Promise((chromeResolve) => {
        chrome.tabs.query({ active: true, currentWindow: true }, (result) => {
          if (chrome.runtime.lastError) {
            console.error("Error querying tabs:", chrome.runtime.lastError);
            chromeResolve([]);
          } else {
            chromeResolve(result);
          }
        });
      });

      const selectedTab = tabs[0];

      if (selectedTab) {
        console.log("The selected tab is " + selectedTab.id);
        resolve(selectedTab);
      } else {
        // If no active tab is found using the initial method, try getting it across all windows
        const activeTabId = await getActiveTabIdAcrossAllWindows();
        if (activeTabId) {
          console.log("The active tab across all windows is " + activeTabId);
          resolve({ id: activeTabId });
        } else {
          reject(new Error("No active tabs found"));
        }
      }
    } catch (error) {
      console.error("Error in getSelectedTab:", error);
      reject(error);
    }
  });
}

//TBR
function getCurrentActiveTab() {
  return new Promise(async (resolve, reject) => {
    try {
      const windows = await new Promise((chromeResolve) => {
        chrome.windows.getAll({ populate: true }, (result) => {
          chromeResolve(result);
        });
      });

      if (windows.length === 0) {
        reject(new Error("No windows found"));
        return;
      }

      const focusedWindow = windows.find((window) => window.focused);

      if (!focusedWindow) {
        // If no focused window, focus on the first available window
        chrome.windows.update(windows[0].id, { focused: true }, () => {
          // Retry getting the tabs after focusing on the window
          getCurrentActiveTab().then(resolve).catch(reject);
        });
        return;
      }

      const activeTab = focusedWindow.tabs.find((tab) => tab.active);

      if (activeTab) {
        resolve(activeTab);
      } else {
        reject(new Error("No active tab found in the focused window"));
      }
    } catch (error) {
      reject(error);
    }
  });
}

function reloadContentScript(tabId) {
  chrome.tabs.get(tabId, function (tab) {
    if (tab.url && !tab.url.includes("chrome://")) {
      iLog("call reload")
    }
  });
}

chrome.runtime.onInstalled.addListener(function () {
  iLog("chrome.runtime.onInstalled.addListener");
});

chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
  // Reload the content script when the tab is updated (e.g., URL changes)
  if (changeInfo.status === "complete" && tab.active) {
    
    getLastTab();

    if (tab.url && !tab.url.includes("chrome://")) {
      iLog("onUpdated.addListener");

      if (isKTStarted) {
        startKT(tab.id);
      }

     /* if (isSpyElementStarted) {
        startSpyElment(tab.id);
      }*/

      if (!contentScriptReloaded) {
        iLog("Reload Content.js");
        iLog(`Tab info : Id: ${tab.id}, url :${tab.url},Index ${tab.index}`);
        reloadContentScript(tab.id);
        contentScriptReloaded = true;
      }
    }
  }
});

//TBR
chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
  // var currentTab = tabs[0];
  // CurrentTab = tabs[0];
  // console.log("current tab:", currentTab);
});

chrome.tabs.onCreated.addListener(function (tab) {
  iLog("New tab created:", tab.id);
  if (tab.url && !tab.url.includes("chrome://"))
  {
      if (isKTStarted) {
        startKT(tab.id);
      }
      /*if (isSpyElementStarted) {
        startSpyElment(tab.id);
      }*/
  }
});

chrome.windows.onCreated.addListener(() => {
  // Get the currently active tab when a new window is created
  getLastTab();
});

chrome.tabs.onActivated.addListener(function (activeInfo) {
  iLog(" onActivated.addListener Tab switched. Tab ID: " + activeInfo.tabId);

  chrome.tabs.get(activeInfo.tabId, function (tab) {
    if (tab.url && !tab.url.includes("chrome://")) {

      if (isKTStarted) {
        startKT(tab.id);
      }
      /*if (isSpyElementStarted) {
        startSpyElment(tab.id);
      }*/
    }
    if (chrome.runtime.lastError || !tab.url || tab.url.includes("chrome://")) {
      iLog(`error onActivated`);
    } else {
      //CurrentTab = tab;
      iLog(`Tab info : Id: ${tab.id}, url :${tab.url},Index ${tab.index}`);
      //activeTabId = tab.id;
      if (!contentScriptReloaded) {
        iLog("Reload Content.js");
        reloadContentScript(tab.id);
        contentScriptReloaded = true;
      } else {
        iLog("Content script already reloaded for this tab.");
      }
    }
  });
});

async function onNativeMessage(request) {
  iLog(`Received message from native host`, JSON.stringify(request, null, 2));
  let result;

  const params = {
    action: request.action.toUpperCase(),
    tabId: request.tabId,
    xpath: request.xPath,
    value: request.value,
    timeout: request.timeout,
    attribute: request.attribute,
    //For WebScraping
    scrapeConfig: request.scrapeConfig
  };
  iLog(`params`, JSON.stringify(params, null, 2));

  if (!params.action) {
    iLog("Empty message received");
    result = "Empty message (action) received";
    return result;
  }

  switch (params.action) {
    case "STARTKT":
      result = await handleStartKT(params);
      break;

    case "STOPKT":
      result = await handleStopKT(params);
      break;

    case "EXECUTEJS":
      result = await handleExecuteAction(params);
      break;

    case "GOTOURL":
      result = await handleGoToUrl(params);
      break;

    case "GETACTIVETAB":
      result = await handleGetActiveTab(params);
      /*if (!isPageReloaded) {
        reloadTab();
        isPageReloaded = true;
      }*/
      break;

    case "GETLASTTAB":
      result = await handleGetLastTab(params);
      break;

    case "GETALLTABS":
      result = await handleGetAllTabs(params);
      break;

    case "GETACTIVEWINDOW":
      result = await handleGetActiveWindow(params);
      break;

    case "OPENNEWTAB":
      result = await handleOpenNewTab(params);
      break;

    case "CLOSETAB":
      result = await handleCloseTabbyId(params);
      break;

    case "CLOSEAPPLICATION":
      result = await handleCloseApplication(params.action);
      break;

    case "SWITCHTAB":
      result = await handleSwitchTad(params);
      break;
    case "SCRAPEDATA":
      result = await handleExecuteWebScraping(params);
      break; 
    case "GETLASTDOWNLOADEDFILE":
      result = await await getLastDownloadedFile(params);
      break;  

    case "HIGHLIGHTELEMENT":
    case "SPYELEMENT":
    case "STOPSPYELEMENT":
        //On applique le highlight sur la page active
        let currentActiveTab = await getSelectedTab();
        params.tabId = currentActiveTab.id;
        result = await handleExecuteAction(params);
        break;  
  
     /* result = await handleStartSpyElement(params);
      break;
      result = await handleStopSpyElement(params);
      break;*/

    default:
      result = await handleExecuteAction(params);
      if (params.action.includes("CLICK")) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
  }
  return result;
}

async function getLastDownloadedFile(params) {
  return new Promise((resolve, reject) => {
    chrome.downloads.search({ orderBy: ["-startTime"], limit: 1 }, (results) => {
      
      if (chrome.runtime.lastError) {
        reject(new Error("Cant not get Last Downloaded File : " + chrome.runtime.lastError.message));
        return;
      }
      let response = createResponseObject();
      let  lastDownload;
      let isHistoriqueDownloeadFileExist = false;
      if (results && results.length > 0) {
        lastDownload = results[0];
        isHistoriqueDownloeadFileExist = true;
      } else {
        lastDownload = {
                        id: -1,
                        filename: "",
                        exists: false
                      };
      }
      response.success = true;
      response.isExist = isHistoriqueDownloeadFileExist;
      response.value = JSON.stringify(lastDownload);
      resolve(response);
    });
  });
}


async function ensureTabExists(params)
{
  let tabId = params.tabId;
  let CurrentActiveTab = await getSelectedTab();
  //Si le tabId n'est pas fourni, on utilise la tab active.
  if(!tabId || tabId == TAB_NOT_INDECATED)
    {
      tabId = CurrentActiveTab.id;
    } else
    {
      const tabExist = await doesTabExist(params.tabId);
  
      if (tabExist == null) {
        throw new IRISException(
            `TabNotFoundIrisException`,
            `Tab Id: ${params.tabId} does not exist`
        );
      }
       //si l'onglet du travail n'est pas active on l'active
      if(tabId != CurrentActiveTab.id)
      {
        await switchToTab(tabId).then((res) => {
          return res.message;
        })
        .catch((error) => {
          iLog("Not error, this is just an warnning : Failed to switch tab:", error);
        });
      }

    }
    return tabId;
}

async function handleExecuteAction(params) {

  params.tabId= await ensureTabExists(params);

  if (!params.action) {
    throw new IRISException(`ActionTypeIrisException`, `Action type is empty`);
  }

  iLog(`Request : ${params}, Tab Id : ${params.tabId}`);

  let response = await executeAction(params);

  return response;
}

async function handleExecuteWebScraping(params) {
  iLog(`Execute Web Scraper`);

  params.tabId = await ensureTabExists(params);

  iLog(`Request : ${params}, Tab Id : ${params.tabId}`);

  let response = await executeWebScraping(params);

  return response;
}

async function waitSpyElementRessponse() {
  iLog("Waiting for spy element response...");
  return new Promise((resolve) => {
    const messageListener = (message, sender, sendResponse) => {
      if (message.action === "spyElementResponse") {
        // Retirer l'écouteur une fois le message reçu

        chrome.runtime.onMessage.removeListener(messageListener);
        resolve(message.data); // Résoudre la promesse avec les données du message
      }
    };

    // Ajouter l'écouteur de message
    chrome.runtime.onMessage.addListener(messageListener);
  });
}

async function handleStartSpyElement(params) {
  let result = createResponseObject();
  if (isSpyElementStarted) {
    result.success = false;
    result.message = "Spy Element is already started";
    return result;
  }
  isSpyElementStarted = true;
  chrome.tabs.query({}, (tabs) => {
    for (const tab of tabs) {
      if (tab.url && !tab.url.includes("chrome://")) {
        startSpyElment(tab.id);
      }
    }
  });
  result = await waitSpyElementRessponse();
  isSpyElementStarted = false;
  handleStopSpyElement(params);
  return result;
}

async function handleStopSpyElement(params) {
  const result = createResponseObject();
  if (!isSpyElementStarted) {
    result.success = false;
    result.message = "Spy Element is already stopped";
    return result;
  }
  isSpyElementStarted = false;
  chrome.tabs.query({}, (tabs) => {
    for (const tab of tabs) {
      stopSpyElement(tab.id);
    }
  });
  isSpyElementStarted = false;
  result.success = true;
  return result;
}

async function handleStartKT(params) {
  const result = createResponseObject();
  if (isKTStarted) {
    result.success = false;
    result.message = "KT is already started";
    return result;
  }


  KTSocket = CommunicationFactory.createCommunication(communicationType, ModeConnexion.IRISKT);
  (async () => {

    await KTSocket.connect();

  })();


  chrome.tabs.query({}, (tabs) => {
    for (const tab of tabs) {
      if (tab.url && !tab.url.includes("chrome://")) {
        startKT(tab.id);
      }
    }
  });
  isKTStarted = true;
  result.success = true;
  return result;
}


async function handleStopKT(params) {
  const result = createResponseObject();
  if (!isKTStarted) {
    result.success = false;
    result.message = "KT is already stopped";
    return result;
  }
  KTSocket.disconnect();
  chrome.tabs.query({}, (tabs) => {
    for (const tab of tabs) {
      if(tab.url && !tab.url.includes("chrome://"))
      {
        stopKT(tab.id);
      }
    }
  });
  isKTStarted = false;
  result.success = true;
  return result;
}

async function handleGetActiveTab(params) {
  let response = createResponseObject();
  try {
    let CurrentActiveTab = await getSelectedTab();
    console.log("Current Active Tab: ", CurrentActiveTab);
    // iLog("Active Tab: ", CurrentActiveTab.id);

    response.message = "tab id is " + CurrentActiveTab.id;
    response.success = true;
    response.isExist = true;
    response.value = CurrentActiveTab.id;
    response.pageId = CurrentActiveTab.id;
    response.idStep = params.action;
    response.text = CurrentActiveTab.url;

    iLog(response.message);
    return response;

  } catch (error) {
    console.log("Error: ", error);

    throw new IRISException(
        `TabNotFoundIrisException`,
        `There is no active tab. error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function handleGetLastTab(params) {
  let response = createResponseObject();
  try {
    let CurrentActiveTab = await getLastTab();
    console.log("last Tab: ", CurrentActiveTab);
    // iLog("Active Tab: ", CurrentActiveTab.id);

    response.message = "tab id is " + CurrentActiveTab.id;
    response.success = true;
    response.isExist = true;
    response.value = CurrentActiveTab.id;
    response.pageId = CurrentActiveTab.id;
    response.idStep = params.action;
    response.text = CurrentActiveTab.url;

    iLog(response.message);
    return response;

  } catch (error) {
    console.log("Error: ", error);

    throw new IRISException(
        `TabNotFoundIrisException`,
        `There is no active tab. error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function handleGetAllTabs(params) {
  let response = createResponseObject();

  try {

    let CurrentActiveTab = await getAllTabs();
    console.log("last Tab: ", CurrentActiveTab);
    // iLog("Active Tab: ", CurrentActiveTab.id);

    response.message = "tab id is " + CurrentActiveTab.id;
    response.success = true;
    response.isExist = true;
    response.value = CurrentActiveTab.url;
    response.pageId = CurrentActiveTab.id;
    response.idStep = params.action;
    response.text = "";

    iLog(response.message);
    return response;

  } catch (error) {
    console.log("Error: ", error);

    throw new IRISException(
        `TabNotFoundIrisException`,
        `There is no active tab. error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function handleGoToUrl(params) {
  let response = createResponseObject();

  params.tabId = await ensureTabExists(params);
  const tabId = params.tabId;
  const url = params.value;
  const action = params.action;

  if (!url) {
    throw new IRISException(`UrlIrisException`, `Url is empty`);
  }

  try {
    // checking if the url is valid
    new URL(url);
  } catch (error) {
    throw new IRISException(
        `UrlIrisException`,
        `The url ${url} is not valid. Please add a protocol (like http, https, ...). 
      error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }

  // Update the URL
  await updateUrl(tabId, url);

  // Wait for the page to finish loading
  await new Promise(resolve => {
    chrome.webNavigation.onCompleted.addListener(function listener(details) {
      if (details.tabId === tabId) {
        chrome.webNavigation.onCompleted.removeListener(listener);
        resolve();
      }
    });
  });

  response.message = "URL changed on tab, and the page is loaded";
  response.success = true;
  response.isExist = true;
  response.value = url;
  response.pageId = tabId;
  response.idStep = action;

  response.text = "";

  iLog(response.message);

  return response;
}

async function handleOpenNewTab(params) {
  let response = createResponseObject();

  const url = params.value;
  const action = params.action;

  if (!url) {
    throw new IRISException(`UrlIrisException`, `Url is empty`);
  }

  //TODO return url.startsWith("http://") || url.startsWith("https://");

  try {
    //checking if the url is valid
    new URL(url);

    const newTabId = await openNewTab(url);

    response.message = `open a new tab`;
    response.success = true;
    response.isExist = true;
    response.value = url;
    response.pageId = newTabId;
    response.idStep = action;
    response.text = "";

    iLog(response.message);

    return response;
  } catch (error) {
    throw new IRISException(
        `UrlIrisException`,
        `The url ${url} is not valid please add a protocol (like http,https, ...). 
      error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function handleCloseTabbyId(params) {
  let response = createResponseObject();
  params.tabId = await  ensureTabExists(params);
  const tabId = params.tabId;
  const action = params.action;
  await closeTabById(tabId);

  response.message = `close tab`;
  response.success = true;
  response.isExist = true;
  response.value = tabId;
  response.pageId = tabId;
  response.idStep = action;
  response.text = "";

  iLog(response.message);

  return response;

}

async function handleGetActiveWindow(params) {
  iLog(`Response getActiveWindow : ${params.action}`);

  /*chrome.windows.getAll({ populate: false }, function (windows) {
    let listWindows = windows.map((window) => window.id);
    iLog(`Response getActiveWindow : ${listWindows.toString()}`);
    return (listWindows.toString());
  });*/

}

async function handleCloseApplication(action) {
  let response = createResponseObject();

  closeAllTabs()
      .then((message) => {
        console.log(message);

        response.message = message;
        response.success = true;
        response.isExist = true;
        response.value = true;
        response.pageId = 0;
        response.idStep = action;
        response.text = "message";

        iLog(message);

        return response;
      })
      .catch((error) => {
        iLog(error.message);

        throw new IRISException(
            `TabNotFoundIrisException`,
            `There is no active tab to close, error: ${error.message} \nStacktrace: ${error.stack}`
        );
      });
}

async function handleSwitchTad(params) {
  let response = createResponseObject();

  const tabId = params.tabId;
  const action = params.action;

  switchToTab(tabId)
      .then((res) => {
        console.log(res.message);

        response.message = res.message;
        response.success = true;
        response.isExist = true;
        response.value = true;
        response.pageId = tabId;
        response.idStep = action;
        response.text = "response.message";

        iLog(message);

        return response;
      })
      .catch((error) => {
        iLog("Failed to switch tab:", error);
      });
}


//TBR
function handleUnknownAction(action) {
  throw new IRISException(
      `UnknownActionIrisException`,
      `The action : ${action} is not implemented.`
  );
}

/* Extension Tab Functions
 *
 */

async function doesTabExist(tabId) {
  return new Promise((resolve) => {
    chrome.tabs.get(tabId, (tab) => {
      resolve(!!tab);
    });
  });
}

async function updateUrl(tabId, url) {
  return new Promise((resolve) => {
    chrome.tabs.update(tabId, { url: url }, () => {
      iLog(`Update Url : ${url}`);
      resolve(url);
    });
  });
}

async function openNewTab(url) {
  return new Promise((resolve) => {
    chrome.tabs.create({ url: url }, (tab) => {
      iLog(`Open New Tab : ${url}`);
      resolve(tab.id);
    });
  });
}

function closeTabById(tabId) {
  return new Promise((resolve, reject) => {
    chrome.tabs.remove(tabId, function () {
      if (chrome.runtime.lastError) {
        reject(
            new IRISException("ChromeIrisException", chrome.runtime.lastError)
        );
      } else {
        iLog(`Closed Tab with ID: ${tabId}`);
        resolve();
      }
    });
  });
}

function closeAllTabs() {
  return new Promise((resolve, reject) => {
    chrome.tabs.query({}, async (tabs) => {
      for (const tab of tabs) {
        await closeTab(tab.id);
      }
      resolve("All tabs closed successfully");
    });
  });
}

function closeTab(tabId) {
  return new Promise((resolve, reject) => {
    chrome.tabs.remove(tabId, () => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(`Tab with ID ${tabId} closed`);
      }
    });
  });
}

function switchToTab(tabId) {
  return new Promise((resolve, reject) => {
    chrome.tabs.update(tabId, { active: true }, () => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve({ success: true, message: `Switched to tab with ID ${tabId}` });
      }
    });
  });
}

function getAllFrames(tabId){
  return new Promise((resolve, reject) => {
    chrome.webNavigation.getAllFrames({ tabId: tabId }, (frames) => {
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      resolve(frames);
    });
  });
}

function reloadTab() {
  chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
    if (tabs.length > 0) {
      chrome.tabs.reload(tabs[0].id);
    }
  });
}

/* Interaction With Content JS
 *
 */

async function executeAction(params) {
  let response = createResponseObject();
  const action = params.action.toUpperCase();
  const tabId = params.tabId;

  const request = {
    action: action,
    xpath: params.xpath, // Used in IS EXIST, CLICK LEFT, CLICK RIGHT, DOUBLE CLICK, ...
    value: params.value, // Used in TYPE INTO
    attribute: params.attribute
  };

  console.log("tab info :", tabId);

  try {
    const result = await sendMessageToContentPage(tabId, request);
    if (!result.success) {
      throw new IRISException(
          `ActionExecutionIrisException`,
          ` ${result.message}`
      );
    }

    response.message = result.message;
    response.text = "result.text";
    response.isExist = result.isExist;
    response.success = true;
    response.pageId = tabId;
    response.idStep = action;
    response.value = result.value;

    console.log("response : ", result);
    iLog(`Response ${action} : ${result.text}`);

    return response;
  } catch (error) {
    throw new IRISException(
        `ActionExecutionIrisException`,
        `Couldn't execute ${action}, error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function executeWebScraping(params){
  let response = createResponseObject();
  const action = params.action.toUpperCase();
  const tabId = params.tabId;
  const scrapeConfig = params.scrapeConfig;
  const request = {
    action: params.action.toUpperCase(),
    elements: scrapeConfig.elements,
    pagination: scrapeConfig.pagination,
    count: scrapeConfig.count,
    xpath: params.xpath
  };

  try {
    const result = await sendMessageToContentPage(tabId, request);

    if (!result.success) {
      throw new IRISException(
          `ActionExecutionIrisException`,
          ` ${result.message}`
      );
    }
    
    //result of datatable
    response.message = result.message;
    response.text = result.message;
    response.isExist = result.isExist;
    response.success = result.success;
    response.pageId = tabId;
    response.idStep = action;
    response.value = result.value;

    console.log("response : ", result);
    iLog(`Response ${action} : ${result.text}`);

    return response;
  } catch (error) {
    throw new IRISException(
        `ActionExecutionIrisException`,
        `Couldn't execute ${action}, error: ${error.message} \nStacktrace: ${error.stack}`
    );
  }
}

async function sendMessageToContentPage(tabId, request)
{
    // Si l'action doint s'executer dans la page principale (et pas dans un frame de cete page)
    if(actions_use_pricipal_page.includes(request.action.toUpperCase()))
    {
      return await sendMessageToContentPageIframe(tabId, request, ID_PRINCIPALe_PAGE);
    }
    else if(request.xpath)  //Si l'action se base sur l'xpath (cas de click, write, highlight, ...), on cherche l'iframe contenant l'element concerné par l'action
    {
        let frameId = await detectFrameId(tabId, request);
        return await sendMessageToContentPageIframe(tabId, request, frameId);
    }
    else //Sinon on envoie la demande à toutes les pages (cas Start KT, Stop KT, Spy Elemenet, presse key)
    {
          return await sendMessageToContentPageAllIframe(tabId, request);
    }
}

//Envoyer la requette à une iframe specifique de la page tabId .
async function sendMessageToContentPageIframe(tabId, request, frameId)
{
  return await chrome.tabs.sendMessage(tabId, request, { frameId: frameId });
}

//Envoyer la requette à toutes les iframes de la page tabId (cas du KT et Spy Element, ...) .
async function sendMessageToContentPageAllIframe(tabId, request)
{
  return await chrome.tabs.sendMessage(tabId, request);
}



async function detectFrameId(tabId, request) 
{
  try
  {

  let  allFrames = await getAllFrames(tabId);

  //on garde juste les iframes actives et exploitables par l'utilisateur
  allFrames = allFrames.filter(frame => 
    (frame.documentLifecycle === "active" || frame.documentLifecycle === "prerender") &&
    frame.frameId >= 0
  );

  // Cas d'une seule page principale n'ayant pas des iframes
  if(allFrames.length == 1)
  {
    return allFrames[0].frameId;
  }
  else // Cas de plusieurs iframes
  {
    let framesCandidates= [];

    //Chercher les iframes qui sont visible à l'utilisateur et ayant l'element request.xpath
    let requestCheckIframe = {
      id: request.id, 
      action: "IsExist", //IsVisible      
      tabId: tabId,                                  
      xpath: request.xpath, 
      value: "",                                 
      attribute: "",                             
      timeout: 2                              
    };
  
    //Chercher les ifrmaes visibles pour l'utilisateur et qui contient l'element indiqué par l'xpath
    for (let frame of allFrames)
    { 
       try
       {
            requestCheckIframe.frameId = frame.frameId;
            let result = await sendMessageToContentPageIframe(tabId, requestCheckIframe, frame.frameId); 
            if (result.success && result.isExist)
            {
              framesCandidates.push(frame); 
            }
        }catch(error)
       {
            iLog(`Warning: Error during iframe detection. Can't communicate with iframe ${JSON.stringify(frame, null, 2)}. Error details: ${error.message} \n: ${error.stack}`);
       } 
    }

    if (framesCandidates.length === 0)
    {
          iLog(`Warning: Error during iframe detection. The main page is being used by default because no iframe contains the element '${request.xpath}'.`);
          return ID_PRINCIPALe_PAGE; // ID par défaut de la page principale, on laisse cette gérer les erreurs s'il y en a.
    }
    else if (framesCandidates.length === 1) 
    {
          return framesCandidates[0].frameId; // Retourner l'ID unique si une seule frame correspond
    } 
    else 
    {
       iLog(`Warning: The page contains multiple iframes. There are ${framesCandidates.length} iframes containing the same requested element (xpath='${request.xpath}') for the action '${request.action}'.
              The action will be executed in the first iframe ${JSON.stringify(framesCandidates[0], null, 2)}. In case of an error, please consider using a unique xpath or utilizing the advanced Web 'Execute Script' skill.`);

        return framesCandidates[0].frameId; // Retourner l'ID de la première frame trouvée
    }

  }

}catch(error)
{
  iLog(`Warning: Error during iframe detection. The main page is being used. TabId ${tabId}. Error details: ${error.message} \n: ${error.stack}`);
  return ID_PRINCIPALe_PAGE; // ID par defaut de la page principale
}
}

//SEND RESULT TO IRIS
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "sendRecordedStep") {
    request.message.parameters.tabId = sender.tab.id;
    sendMessageToKTServer(request);
  }

  if (request.action === "logFromContentScript") {
    iLog(`is ${request.message}`);
    //return false;
  }

  if (request.type === "ActionExecutionIrisException") {
    throw new IRISException(request.type, request.message);
  }
});

function sendMessageToKTServer(request) {
  try {
    request.message.destination = "IRIExecuterKT"+KTSocket.getWindowsSessionNameUniforme();
    KTSocket.sendJsonMessage(request.message);
  } catch (error) {
    console.error("Error sending message:", error);
  }
}
