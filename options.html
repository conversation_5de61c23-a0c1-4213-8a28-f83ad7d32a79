<!DOCTYPE html>
<html>
<head>
    <title>Extension Config</title>
    <script src="options.js" defer></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 20px auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 2px 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            font-size: 20px;
            text-align: center;
        }
        label {
            display: block;
            margin-top: 10px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        small {
            display: block;
            color: #555;
            font-size: 12px;
            margin-top: 2px;
        }
        button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 4px;
            margin-top: 15px;
            cursor: pointer;
        }
        #save {
            background-color: #28a745;
            color: white;
        }
        #save:hover {
            background-color: #218838;
        }
        #reset {
            background-color: #dc3545;
            color: white;
        }
        #reset:hover {
            background-color: #c82333;
        }
        #message {
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>IRIS - Extension Settings Panel</h1>
    <div id="message"></div>

    <label>WebSocket Host:
        <input type="text" id="websocketHost" placeholder="e.g., localhost">
        <small>Enter the WebSocket server host (e.g., localhost).</small>
    </label>
    
    <label>WebSocket Port:
        <input type="number" id="websocketPort" placeholder="e.g., 8976">
        <small>Enter the WebSocket server port (e.g., 8976).</small>
    </label>

    <label>Windows Session name:
        <input type="text" id="windowsSessionName">
        <small>Use <code>cmd</code> and run <code>echo %USERNAME%</code> to get your Windows session name.</small>
    </label>

    <label>Log Level:
        <select id="logLevel">
            <option value="debug">Debug</option>
            <option value="information">Information</option>
        </select>
        <small>Select the log verbosity level: "Debug" for detailed logs, "Information" for standard logs.</small>
    </label>

    <button id="save">Save</button>
    <button id="reset">Reset to Default</button>
</body>
</html>
