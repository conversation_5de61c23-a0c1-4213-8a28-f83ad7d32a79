/* Declaration of public variables
 *
 */
var irisPort = "";
const pageTitle = document.title;

const actions_list = [
  "CLICKLEFT",
  "CLICKRIGHT",
  "DOUBLECLICK",
  "TYPEINT<PERSON>",
  "SELECTVALUE",
  "COPY",
  "CHECKBOX",
  "UNCHECKBOX",
  "READ",
  "PRESSENTER",
  "PRESSTAB",
  "HIGHLIGHTELEMENT",
  "PRESSKEY"
];

/* General Functions
 *
 */

function createResultObject() {
  return {
    text: "",
    value: "",
    isExist: false,
    success: false,
    message: "",
    timeout: 0,
  };
}

function iLog(...args) {
  console.log(`IRIS_CONTENT :: ${args.join(" ")}`);
}

class IRISException extends Error {
  constructor(typeException, message) {
    super(message);
    this.typeException = typeException;
  }
}

function handleErrorMessage(error) {
  let result = createResultObject();

  if (error instanceof IRISException) result.message = error.message;
  else result.message = `Unexpected error: ${error.message}`;

  result.success = false;
  result.text = result.message;
  result.value = result.message;

  return result;
}

async function reloadPage() {
  let result = createResultObject();

  try {
    const beforeUnloadPromise = new Promise((resolve) => {
      window.addEventListener("beforeunload", resolve);
    });

    location.reload();

    await beforeUnloadPromise;

    result.success = true;
    result.message = "Page is reloaded";
    result.text = result.message;
  } catch (error) {
    return handleErrorMessage(
      new IRISException(
        "ActionExecutionIrisException",
        `Couldn't ReloadPage ${error}. \nStacktrace: ${error.stack}`
      )
    );
  }

  return result;
}

async function goBackToPreviousPage() {
  let result = createResultObject();

  try {
    result.success = true;
    result.message = `Go back to the previous page`;
    result.text = result.message;
    result.value = result.message;
  } catch (error) {
    return handleErrorMessage(
      new IRISException(
        "ActionExecutionIrisException",
        `Couldn't go back to the previous page: ${error.message} \nStacktrace: ${error.stack}`
      )
    );
  }

  return result;
}

async function goToNextPage() {
  let result = createResultObject();

  try {
    result.success = true;
    result.message = "Go to the next page";
    result.text = result.message;
    result.value = result.message;
  } catch (error) {
    return handleErrorMessage(
      new IRISException(
        "ActionExecutionIrisException",
        `Couldn't go to the next page: ${error.message} \nStacktrace: ${error.stack}`
      )
    );
  }
  return result;
}

function selectSingleNode(xpath) {
  return document.evaluate(
    xpath,
    document,
    null,
    XPathResult.FIRST_ORDERED_NODE_TYPE,
    null
  ).singleNodeValue;
}

function getNextElementInTabOrder(xpath) {
  const currentElement = selectSingleNode(xpath);

  if (!currentElement) {
    console.error("Element not found for the provided XPath.");
    return null;
  }

  // Get all focusable elements in the document
  const focusableElements = Array.from(document.querySelectorAll(
    'a[href], area[href], input:not([disabled]), select:not([disabled]), ' +
    'textarea:not([disabled]), button:not([disabled]), iframe, object, embed, ' +
    '[tabindex], [contenteditable]'
  )).filter(el => el.tabIndex >= 0);

  // Sort elements by their tab index (following natural tab order when tabIndex is 0)
  focusableElements.sort((a, b) => a.tabIndex - b.tabIndex || a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1);

  // Find the index of the current element
  const currentIndex = focusableElements.indexOf(currentElement);

  // Return the next element in tab order
  return focusableElements[currentIndex + 1] || focusableElements[0]; // Loop back to the start if no next element is found
}

async function getDOM() {
  iLog(`getDOM`);
  let result = createResultObject();

  try {
    const domString = document.body;
    var bodyClone = domString.cloneNode(true);

    // Remove scripts, styles, links, and svgs
    var elementsToReplace = bodyClone.querySelectorAll(
      "script, style, link, svg"
    );

    elementsToReplace.forEach(function (element) {
      var replacementElement = document.createElement(element.tagName);
      replacementElement.innerHTML = "";
      element.parentNode.replaceChild(replacementElement, element);
    });

    // Remove all CSS classes
    removeAllClasses(bodyClone);

    if (bodyClone.outerHTML) {
      const outputHtml = bodyClone.outerHTML
        .replace(/"/g, "'")
        .replace(/\\/g, "\\\\");
      result.success = true;
      result.isExist = true;
      result.message = `message body Html`;
      result.text = outputHtml;
      result.value = outputHtml;
    } else {
      throw new IRISException(
        "ActionExecutionIrisException",
        "Couldn't retrieve the DOM"
      );
    }
  } catch (error) {
    return handleErrorMessage(
      new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
    );
  }

  return result;
}

function removeAllClasses(element) {
  if (element.classList) {
    element.classList = [];
  }
  for (var i = 0; i < element.children.length; i++) {
    removeAllClasses(element.children[i]);
  }
}

async function getDOM2() {
  iLog(`getDOM`);
  let result = createResultObject();

  try {
    const domString = document.body;
    var bodyClone = domString.cloneNode(true);
    var elementsToReplace = bodyClone.querySelectorAll(
      "script, style, link, svg"
    );

    elementsToReplace.forEach(function (element) {
      var replacementElement = document.createElement(element.tagName);
      replacementElement.innerHTML = "";
      element.parentNode.replaceChild(replacementElement, element);
    });

    if (bodyClone.outerHTML) {
      const outputHtml = bodyClone.outerHTML
        .replace(/"/g, "'")
        .replace(/\\/g, "\\\\");
      result.success = true;
      result.isExist = true;
      result.message = `message body Html`;
      result.text = outputHtml;
      result.value = outputHtml;
    } else {
      throw new IRISException(
        "ActionExecutionIrisException",
        "Couldn't retrieve the DOM"
      );
    }
  } catch (error) {
    return handleErrorMessage(
      new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
    );
  }

  return result;
}

/* Listeners between Service Worker and Content
 *
 */
chrome.runtime.sendMessage({
  action: "logFromContentScript",
  message: "successfully connected with IRIS_CONTENT",
});

iLog(`is successfully connected with IRIS_WORKER : ${pageTitle}`);

async function sendDomToWebService(port) {
  let result;
  iLog(`sendDomToWebService`);

  try {
    let dom = await getDOM();

    if (dom.value) {
      chrome.runtime.sendMessage({
        action: "callWebServiceDom",
        irisPort: port,
        dom: dom,
      });

      result = {
        success: true,
        message: `Dom was retrieved`,
      };
    } else {
      result = {
        success: false,
        message: `Dom is empty`,
      };
    }

    iLog(result.message);
  } catch (error) {
    console.error("Error retrieving DOM:", error);
    result = {
      success: false,
      message: "Error retrieving DOM \nStacktrace: "+error.stack,
    };
  }
  return result;
}

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  console.log("Content script received a message:", request);

  let action = request.action.toUpperCase();
  let xpath = request.xpath;
  let value = request.value;
  let attribute = request?.attribute ?? "";

  iLog(`action selected: ${action}`);

  if (xpath) {
    Executor.simulateScrollToElement(xpath);
  }

  if (action.includes("CLICK")) {
    (async () => {
      let result = await Executor.simulateMouseClick(xpath, action);
      sendResponse(result);
    })();
    return true;
  } else {
    switch (action) {
      //Actions KT :
      case "STARTKT":
        (async () => {
          let result = await startKT();
          sendResponse(result);
        })();
        return true;

      case "STOPKT":
        (async () => {
          let result = await stopKT();
          sendResponse(result);
        })();
        return true;

      //Actions Spy and HIGHLIGHTELEMENT element :
      case "SPYELEMENT":
        (async () => {
          let spyElement = createSpyAndHighlightElement();
          let result = await spyElement.startSpyElement();
          sendResponse(result);
        })();
        return true;

      case "STOPSPYELEMENT":
        (async () => {
          let spyElement = getSpyAndHighlightElement();
          iLog("stop spy element function");
          let result = await spyElement.stopSpyElement();
          iLog("result before sending the responce in stop spy element: ", result);
          sendResponse(result);
        })();
        return true;

      case "HIGHLIGHTELEMENT":
        (async () => {
          let spyElement = createSpyAndHighlightElement();
          let result = await spyElement.highlightElement(xpath);
          sendResponse(result);
        })();
        return true;

      //Actions Runtime :
      case "GETDOM":
        (async () => {
          let result = await getDOM();
          sendResponse(result);
        })();
        return true;

      case "RELOADPAGE":
        (async () => {
          let result = await reloadPage();
          sendResponse(result);
        })();
        return true;

      case "GOBACKTOPREVIOUSPAGE":
        (async () => {
          let result = await goBackToPreviousPage();
          sendResponse(result);
        })();
        window.history.back();
        return true;

      case "GOTONEXTPAGE":
        (async () => {
          let result = await goToNextPage();
          sendResponse(result);
        })();
        window.history.forward();
        return true;

      case "TYPEINTO":
        (async () => {
          let result = await Executor.simulateTypeInto(xpath, value);
          sendResponse(result);
        })();
        return true;

      case "WRITE":
        (async () => {
          let result = await Executor.simulateTypeInto(xpath, value);
          sendResponse(result);
        })();
        return true;

      case "ISEXIST":
        (async () => {
          let result = await Executor.isElementFound(xpath);
          sendResponse(result);
        })();
        return true;

      case "PRESSTAB":
        (async () => {
          let result = await Executor.simulatePressTab(xpath);
          sendResponse(result);
        })();
        return true;

      case "PRESSDELETE":
        (async () => {
          let result = await Executor.simulatePressDelete(xpath);
          sendResponse(result);
        })();
        return true;

      case "PRESSKEY":
        (async () => {
          let result = await Executor.simulatePressKey(value, xpath);
          sendResponse(result);
        })();
        return true;

      case "PRESSENTER":
        (async () => {
          let result = await Executor.simulatePressEnter(xpath);
          sendResponse(result);
        })();
        return true;

      case "SELECTVALUE":
        (async () => {
          let result = await Executor.simulateSelectOption(xpath, value);
          sendResponse(result);
        })();
        return true;

      case "CHECKBOX":
        (async () => {
          let result = await Executor.simulateCheckBox(xpath);
          sendResponse(result);
        })();
        return true;

      case "UNCHECKBOX":
        (async () => {
          let result = await Executor.simulateUnCheckBox(xpath);
          sendResponse(result);
        })();
        return true;

      case "SELECTRADIO":
        (async () => {
          let result = await Executor.simulateSelectRadio(xpath, value);
          sendResponse(result);
        })();
        return true;

      case "UNSELECTRADIO":
        (async () => {
          let result = await Executor.simulateUnselectRadio(xpath, value);
          sendResponse(result);
        })();
        return true;

      case "EXECUTESCRIPT":
        (async () => {
          await Executor.executeJSCode(value, sendResponse);
        })();
        return true;

      case "READ":
        (async () => {
          let result = await Executor.readFromInput(xpath);
          sendResponse(result);
        })();
        return true;

      case "ISVISIBLE":
        (async () => {
          let result = await Executor.isElementVisible(xpath);
          sendResponse(result);
        })();
        return true;

      case "COPY":
        console.log("pending");
        break;
      case "PASTE":
        console.log("pending");
        break;

      case "GETATTRIBUTE":
        (async () => {
            let result = await Executor.getAttribute(xpath, attribute);
            sendResponse(result);
        })();
        return true;

      case "SETATTRIBUTE":
        (async () => {
            let result = await Executor.setAttribute(xpath, attribute, value);
            sendResponse(result);
        })();
        return true;
  
      case "GETELEMENTPOSITION":
          (async () => {
              let result = await Executor.getElementPosition(xpath);
              sendResponse(result);
          })();
          return true;
  
      case "GETPAGEURL":
          (async () => {
              let result = await Executor.getPageURL();
              sendResponse(result);
          })();
          return true;
  
      case "GETPAGETITLE":
          (async () => {
              let result = await Executor.getPageTitle();
              sendResponse(result);
          })();
          return true;

      case "HOVER":
        (async () => {
            let result = await Executor.simulateHover(xpath);
            sendResponse(result);
        })();
        return true;

      case "FOCUS":
        (async () => {
            let result = await Executor.simulateFocus(xpath);
            sendResponse(result);
        })();
        return true;

      case "SCRAPEDATA":
        (async () => {
          let result = await Executor.executeWebScraping(request);
          await sendResponse(result);
          return true;
        })();
        return true;

      default:
        console.log("Unknown action:", action);
        break;
    }
  }
  return true;
});

/* Executors Functions
 *
 */
class Executor {

  // Utility function to introduce a delay
  static _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Helper function to get element's center coordinates
  static _getElementCenter(el) {
    if(!el || !el.getBoundingClientRect) {
      return {
        x: 0,
        y: 0
      };
    }
    const rect = el.getBoundingClientRect();
    return {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
    };
  }

  /* Mouse Actions
   *
   */
  static _focusOnElement(xpath) {
    const element = selectSingleNode(xpath);
    if (element) {
      element.focus();
      iLog("Focused on element:", element);
    } else {
      console.warn("Element not found with XPath:", xpath);

      throw new IRISException(
        `ElementNotFoundIrisException`,
        `The element with the following XPath: ${xpath}, was not found`
      );
    }
  }
  
  static async _performRightClick(xpath) {
    iLog("clickRight function");
    let result = createResultObject();
    try {
      const element = selectSingleNode(xpath);

      if (element) {

        this._preMouseClick(element);
        iLog("Mouse Right Click");
        this._addEventListeners(element, ["contextmenu"]);

        const rightClickEvent = new MouseEvent("contextmenu", {
          bubbles: true,
          cancelable: true,
          view: window,
          button: 2,
        });

        element.dispatchEvent(rightClickEvent);

        this._removeEventListeners(element, ["contextmenu"]);
        this._postMouseClick(element);

        result.success = true;
        result.message = `Right-click performed on ${xpath}`;
        result.text = result.message;

        iLog(result.message);
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't perform LeftClick on then element: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async triggerEventSequenceOnElement(element, eventSequence, waitBetweenEvents = 100) {
    // wait 400ms before clicking (asynchronously) and in same time let the main thread send the response
     await new Promise(resolve => setTimeout(resolve, 400));
     //focus for interactive elements
     if (element.focus) {
      element.focus();
    }
    for (const eventConfig of eventSequence) {
      // Calculer les coordonnées
      const coords = this._getElementCenter(element);

      // Créer l’événement
      const event = new MouseEvent(eventConfig.type, {
        view: window,
        ...eventConfig.options,
        clientX: coords.x,
        clientY: coords.y
      });

      // Déclencher l’événement
      element.dispatchEvent(event);

      // Attendre un peu entre chaque événement
      await this._delay(waitBetweenEvents);
    }
  // ;
}

static async dispatchDoubleClickEvent(element) {
  await new Promise(resolve => setTimeout(resolve, 400));
  const doubleClickEvent = new MouseEvent("dblclick", {
    view: window,
    bubbles: true,
    cancelable: true,
  });

  element.dispatchEvent(doubleClickEvent);

  this._removeEventListeners(element, ["dblclick"]);
  this._postMouseClick(element);
}

  static async _performDoubleClick(xpath) {
    iLog("new doubleClick function");
    let result = createResultObject();
    try {
      const element = selectSingleNode(xpath);

      if (element) {

        this._updateCaseHrefJS(element);

        this._preMouseClick(element);

        iLog("Mouse Right Click");

        this._addEventListeners(element, ["dblclick"]);

        this.dispatchDoubleClickEvent(element);
        result.success = true;
        result.message = `Double-click performed on ${xpath}`;
        result.text = result.message;

        iLog(result.message);
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't perform double click on then element: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async _performClick(xpath) {
    iLog("_performClick");
    let result = createResultObject();
    const waitBetweenEvents = 20;
    try {
      // Find the element using XPath
      const element = document.evaluate(
        xpath, 
        document, 
        null, 
        XPathResult.FIRST_ORDERED_NODE_TYPE, 
        null
      ).singleNodeValue;

      // Check if element exists
      if (!element) {
        iLog(`Couldn't perform Left-click on ${xpath}`);
        throw new IRISException("ActionExecutionIrisException",
          `Couldn't find element with xpath: ${xpath}`);
      }

      this._updateCaseHrefJS(element);

      // Simulate full user interaction event sequence
      const eventSequence = [
        { type: 'mouseover', options: { bubbles: true, cancelable: true } },
        { type: 'mouseenter', options: { bubbles: true, cancelable: true } },
        { 
          type: 'mousedown', 
          options: { 
              bubbles: true, 
              cancelable: true, 
              which: 1, 
              button: 0 
          } 
        },
        { 
          type: 'mouseup', 
          options: { 
              bubbles: true, 
              cancelable: true, 
              which: 1, 
              button: 0 
          } 
        },
        { 
          type: 'click', 
          options: { 
              bubbles: true, 
              cancelable: true, 
              which: 1, 
              button: 0 
          } 
        }
      ];
      // we need this funcion to be async so don't add await here (Problem solved : click a load a new page and we lost connection with worker before sending response)
      this.triggerEventSequenceOnElement(element, eventSequence, waitBetweenEvents);

      result.success = true;
      result.message = `Left-click performed on ${xpath}`;
      result.text = result.message;
      return result;
    }catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
  }

  static _logEvent(eventName) {
    //console.log(`Event fired: ${eventName}`);
  }

  static _logEventR(eventName) {
    //console.log(`Event removed: ${eventName}`);
  }

  static _addEventListeners(element, events) {
    element._eventListeners = element._eventListeners || {};
    events.forEach((eventName) => {
      const handler = (e) => this._logEvent(eventName, e);
      element.addEventListener(eventName, handler);
      element._eventListeners[eventName] = handler;
    });
  }

  static _removeEventListeners(element, events) {
    if (!element._eventListeners) {
      return;
    }

    events.forEach((eventName) => {
      if (element._eventListeners[eventName]) {
        element.removeEventListener(
          eventName,
          element._eventListeners[eventName]
        );
        //console.log(`Event removed: ${eventName}`);

        delete element._eventListeners[eventName];
      }
    });
  }

  //Le click ne fonctionne pas dans les cas ou l'element a [href="javascript:code JS"]
  //Solution: renommer href à onClick
  static _updateCaseHrefJS(element) 
  {
    if (element) 
    {
      const href = element.getAttribute("href");
      if (href 
          && href.trim().toLowerCase().startsWith("javascript:")
          && !element.hasAttribute("onclick")
      ) 
      {
        element.removeAttribute("href");
        element.setAttribute("onclick", href.trim());
      }
    }
  }

  static _preMouseClick(element) {
    //iLog("pre");
    const eventsToLog = ["mousedown", "focus"];

    this._addEventListeners(element, eventsToLog);

    eventsToLog.forEach((eventName) => {
      element.dispatchEvent(new Event(eventName));
    });

    this._removeEventListeners(element, eventsToLog);
  }

  static _postMouseClick(element) {
    //iLog("post");

    const eventsToLog = ["mouseup", "mouseout", "blur"];

    this._addEventListeners(element, eventsToLog, this._logEvent);

    eventsToLog.forEach((eventName) => {
      element.dispatchEvent(new Event(eventName));
    });

    this._removeEventListeners(element, eventsToLog, this._logEventR);
  }

  static async simulateMouseClick(elementId, action) {
    let result;

    switch (action) {
      case "LEFTCLICK":
        result = await this._performClick(elementId);
        break;

      case "RIGHTCLICK":
        result = await this._performRightClick(elementId);
        break;

      case "DOUBLECLICK":
        result = await this._performDoubleClick(elementId);
        break;

      default:
        result = handleErrorMessage(
          new IRISException(
            "ActionExecutionIrisException",
            "Click action wasn't defined"
          )
        );
        break;
    }

    return result;
  }

  /* Options Actions
   *
   */

  static async simulateSelectOption(xpath, value) {
    iLog(`simulateSelectOption`);
    let result = createResultObject();

    try {
      const element = selectSingleNode(xpath);

      if (element) {
        const optionToSelect = Array.from(element.options).find(
          (option) => option.value === value || option.outerText === value
        );

        if (optionToSelect) {
          element.selectedIndex = optionToSelect.index;

          result.success = true;
          result.isExist = true;
          result.message = `Selected option with value: ${value}`;
          result.text = result.message;
          result.value = true;
        } else {
          throw new IRISException(
            "ActionExecutionIrisException",
            `Option with value ${value} not found in ${xpath}`
          );
        }
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  /* Highlight Actions
   *
   */

  simulateHighlightHtmlElement(elementHTML) {
    if (elementHTML) {
      if (elementHtml.style) {
        if (!elementHtml.style.border) {
          elementHtml.style.border = "3px solid red";
        }
      } else {
        elementHtml.setAttribute("style", "border: 3px solid red;");
      }
    } else {
      iLog("Cannot highlight the element");
    }
  }

  /**  highlightElement(xpath) {
      const elementHtml = selectSingleNode(xpath);
      if (elementHtml) {
        if (elementHtml.style) {
          if (!elementHtml.style.border) {
            elementHtml.style.border = "2px solid red";
          }
        } else {
          elementHtml.setAttribute("style", "border: 2px solid red;");
        }
      }
      setTimeout(function () {
        element.style.border = "none";
      }, 10000);
    } */

  /* Copy/Past Actions
   *
   */

  simulateCopy(xpath) {
    const element = selectSingleNode(xpath);
    if (element) {
      const value = element.textContent;
      try {
        navigator.clipboard.writeText(value);
      } catch (err) {
        console.error("Unable to copy text to clipboard", err);
      }
    } else {
      console.error("Element not found");
    }
  }

  simulatePaste(xpath) {
    //TODO implement paste
  }

  /* Keyboard Actions
   *
   */

  static async simulatePressTab(xpath) {
    let result = createResultObject();
    try {
      const element = getNextElementInTabOrder(xpath);

      if (element) {

        console.log('Next element in tab order:', element);

        element.focus();

        const tabDownEvent = new KeyboardEvent("keydown", {
          key: "Tab",
          code: "Tab",
          keyCode: 9,
          bubbles: true,
          cancelable: true,
        });

        const tabUpEvent = new KeyboardEvent("keyup", {
          key: "Tab",
          code: "Tab",
          keyCode: 9,
          bubbles: true,
          cancelable: true,
        });

        element.dispatchEvent(tabDownEvent);
        element.dispatchEvent(tabUpEvent);

        result.success = true;
        result.isExist = true;
        result.message = `TAB performed on ${xpath}`;
        result.text = result.message;
        result.value = true;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't perform Tab press on ${xpath}: No next element found in tab order.`
        );
      }
    } catch (error) {
      iLog(error.message);
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async simulatePressDelete(xpath) {
    let result = createResultObject();
    try {
      const element = selectSingleNode(xpath);

      if (element) {
        const deleteEvent = new KeyboardEvent("keydown", {
          key: "Delete",
          bubbles: true,
          cancelable: true,
          view: window,
        });

        element.dispatchEvent(deleteEvent);

        result.success = true;
        result.isExist = true;
        result.message = `Delete key pressed on ${xpath}`;
        result.text = result.message;
        result.value = true;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't perform Delete key press on ${xpath}`
        );
      }
    } catch (error) {
      iLog(error.message);

      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async simulatePressKey(value, xpath) {
    let result = createResultObject();

    try 
    {
        const element = selectSingleNode(xpath);
        if (element) {
          const deleteEvent = new KeyboardEvent("keydown", {
            key: value,
            bubbles: true,
            cancelable: true,
            view: window,
          });

          element.dispatchEvent(deleteEvent);

          result.success = true;
          result.isExist = true;
          result.message = `${value} key pressed on ${xpath}`;
          result.text = result.message;
          result.value = true;
        } else {
          throw new IRISException(
            "ActionExecutionIrisException",
            `Couldn't perform press key ${value} on ${xpath}`
          );
        }

    } catch (error) {
      iLog(error.message);
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async simulatePressEnter(xpath) {
    let result = createResultObject();
    try {
      const element = selectSingleNode(xpath);

      if (element) {
        let enterKeyEvent = new KeyboardEvent("keydown", {
          key: "Enter",
          keyCode: 13,
          bubbles: true,
          cancelable: true,
        });

        element.dispatchEvent(enterKeyEvent);

        result.success = true;
        result.isExist = true;
        result.message = `Enter performed on ${xpath}`;
        result.text = result.message;
        result.value = true;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't perform Enter press on ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static checkFormAncestor(element) {
    let ancestry = element.parentElement;

    while (ancestry !== null) {
      if (ancestry.tagName === "FORM") {
        Executor.submitForm(ancestry);
        return true;
      }
      ancestry = ancestry.parentElement;
    }
    return false;
  }

  static submitForm(form) {
    var evt = document.createEvent("Event");
    evt.initEvent("submit", true, true);
    if (form.dispatchEvent(evt)) {
      form.submit();
    }
  }

  /* CheckBox
   *
   */

  static async simulateCheckBox(xpath) {
    iLog(`simulateCheckBox`);
    let result = createResultObject();

    try {
      const element = selectSingleNode(xpath);

      if (element) {

        if (element.type !== 'checkbox') {
          throw new IRISException("ActionExecutionIrisException", `The element at '${xpath}' is not a HTML checkbox (tag input with type = 'checkbox'). Please verify your XPath or use another skill (such as 'click') to interact with 'the checkbox' !`);
        }

        if (!element.checked) {
          element.click();
          result.message = `The element ${xpath} was checked`;
        } else {
          result.message = `The element ${xpath} was already checked`;
        }

        result.success = true;
        result.isExist = true;
        result.text = result.message;
        result.value = element.checked;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static async simulateUnCheckBox(xpath) {
    iLog(`simulateCheckBox`);
    let result = createResultObject();

    try {
      const element = selectSingleNode(xpath);

      if (element) {
        if (element.type !== 'checkbox') {
          throw new IRISException("ActionExecutionIrisException", `The element at '${xpath}' is not a HTML checkbox (tag input with type = 'checkbox'). Please verify your XPath or use another skill (such as 'click') to interact with 'the checkbox' !`);
        }
        if (element.checked) {
          element.click();
          result.message = `The element ${xpath} was unchecked`;
        } else {
          result.message = `The element ${xpath} was already unchecked`;
        }
        result.success = true;
        result.isExist = true;
        result.text = result.message;
        result.value = element.checked;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static simulateScrollToElement(xpath) {
    const element = selectSingleNode(xpath);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "nearest",
      });
      iLog(`Scroll to : ${xpath}`);
    } else {
      iLog("Element not found with the given XPath:", xpath);
    }
  }

  static async simulateTypeInto(xpath, value) {
    iLog(`simulateTypeInto`);
    let result = createResultObject();

    try {
      const element = selectSingleNode(xpath);

      if (element) {
        element.value = value;
        const events = [
          "mouseover", "mousemove", "mousedown", "focus", "mouseup", "click",
          "keyup", "input", "keydown", "keypress", "mousemove", "mouseout",
          "change", "blur"
        ];
        events.forEach(event => {
          const evt = new Event(event, { bubbles: true, cancelable: true });
          element.dispatchEvent(evt);
        });
        result.success = true;
        result.isExist = true;
        result.message = `Typed "${value}" into element with XPath: ${xpath}`;
        result.text = result.message;
        result.value = value;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Element with XPath ${xpath} not found`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static emulateTyping(xpath, value, callback) {
    const delay = 100; // Delay in milliseconds between key strokes
    let result = {};

    iLog(`i'm here typing into`);
    const element = selectSingleNode(xpath);

    // Function to simulate a single key press
    function simulateKeyPress(character) {
      try {
        // Create and dispatch keydown event
        const keydownEvent = new KeyboardEvent("keydown", {
          key: character,
          bubbles: true,
        });
        element.dispatchEvent(keydownEvent);

        // Create and dispatch keypress event
        const keypressEvent = new KeyboardEvent("keypress", {
          key: character,
          bubbles: true,
        });
        element.dispatchEvent(keypressEvent);

        // Update the value of the element and dispatch input event
        element.value += character;
        const inputEvent = new Event("input", { bubbles: true });
        element.dispatchEvent(inputEvent);

        // Create and dispatch keyup event
        const keyupEvent = new KeyboardEvent("keyup", {
          key: character,
          bubbles: true,
        });
        element.dispatchEvent(keyupEvent);
      } catch (error) {
        result = {
          success: false,
          message: `Error on typing: ${error.message} \nStacktrace: ${error.stack}`,
        };
        callback(result);
        throw error; // Stop the execution if an error occurs
      }
    }

    // Function to simulate typing the entire string
    function typeString(index) {
      if (index < value.length) {
        simulateKeyPress(value[index]);
        setTimeout(() => typeString(index + 1), delay);
      } else {
        // Dispatch change event after typing is finished
        const changeEvent = new Event("change", { bubbles: true });
        element.dispatchEvent(changeEvent);

        result = {
          success: true,
          message: "Typing completed successfully.",
        };
        callback(result);
      }
    }

    try {
      typeString(0);
    } catch (error) {
      // Error handling is already done in the simulateKeyPress function
    }
  }

  static async isElementFound(xpath) {
    iLog(`isElementFound`);

    let result = createResultObject();
    const element = selectSingleNode(xpath);

    try {
      if (element) {
        result.success = true;
        result.isExist = true;
        result.value = "true"; //value est un string cote C#
        result.message = `Element was found ${xpath}`;
        result.text = true;

        iLog(`the element ${xpath} was found`);
      } else {
        result.success = true;
        result.isExist = false;
        result.value = "false"; //value est un string cote C#
        result.message = `Element was not found ${xpath}`;
        result.text = false;
      }
    } catch (error) {
      iLog(error.message);
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static async isElementFound(xpath) {
    iLog(`isElementFound`);

    let result = createResultObject();
    const element = selectSingleNode(xpath);

    try {
      if (element) {
        result.success = true;
        result.isExist = true;
        result.value = "true"; //value est un string cote C#
        result.message = `Element was found ${xpath}`;
        result.text = true;

        iLog(`the element ${xpath} was found`);
      } else {
        result.success = true;
        result.isExist = false;
        result.value = "false"; //value est un string cote C#
        result.message = `Element was not found ${xpath}`;
        result.text = false;
      }
    } catch (error) {
      iLog(error.message);
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  /**  TODO : à supprimer une fois le nouveau highlightElement est validé
   * static async highlightElement(xpath) {
     iLog(`highlightElement`);
 
     let result = createResultObject();
     const element = selectSingleNode(xpath);
 
     try {
       if (element) {
         const originalTransition = element.style.transition;
         const originalBorder = element.style.border;
 
         element.style.transition = 'border-color 0.2s ease'; // Faster transition
         element.style.borderWidth = '2px'; // Ensure the border is visible
         element.style.borderStyle = 'solid';
 
         let flashing = true;
         const flashInterval = setInterval(() => {
             element.style.borderColor = flashing ? 'red' : 'blue';
             flashing = !flashing;
         }, 200); // Faster flash
 
         setTimeout(() => {
             clearInterval(flashInterval);
             element.style.border = originalBorder;
             element.style.transition = originalTransition;
         }, 3000); // Total flash duration
 
         iLog(`the element ${xpath} was found`);
       } else {
         result.success = true;
         result.isExist = false;
         result.value = false;
         result.message = `Element was not found ${xpath}`;
         result.text = false;
       }
     } catch (error) {
       iLog(error.message);
       return handleErrorMessage(
           new IRISException("ActionExecutionIrisException", error.message)
       );
     }
 
     return result;
   }
 
 //TODO : à supprimer une fois le nouveau spyElement est validé
  static async spyElement() {
   iLog(`spyElement`);
 
   let result = createResultObject();
   let xpath = "";
 
   try {
     function getElementXPath(element) {
       if (element === document.body) {
           return '/html/body';
       }
   
       const parts = [];
       while (element && element.nodeType === Node.ELEMENT_NODE) {
           let index = 0;
           let sibling = element.previousSibling;
           while (sibling) {
               if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === element.nodeName) {
                   index++;
               }
               sibling = sibling.previousSibling;
           }
           const tagName = element.nodeName.toLowerCase();
           const part = `${tagName}[${index + 1}]`;
           parts.unshift(part);
           element = element.parentNode;
       }
   
       return parts.length ? '/' + parts.join('/') : null;
    }
 
     function handleClick(event) {
       event.preventDefault(); // Prevent default action (e.g., navigating to a link)
       event.stopPropagation(); // Stop the event from bubbling up
 
       const element = event.target;
       xpath = getElementXPath(element);
       console.log('XPath:', xpath); // Log the XPath for debugging purposes
       
       console.log('element:', element);
       // Call the highlightElement function
       //highlightElement(xpath);
       Executor.highlightElement(xpath);
 
       // Remove the event listener after the first click
       document.removeEventListener('click', setTimeout(handleClick), 5000);
 
       // Store the XPath in a global variable
       window.capturedXPath = xpath;
   }
     result.success = true;
     result.isExist = true;
     result.message = `Selected xpath with value: ${xpath}`;
     result.text = result.message;
     result.value = xpath;
 
   // Add the event listener
     document.addEventListener('click', handleClick);
 
       iLog(`the element ${xpath} was found`);
 
   } catch (error) {
     iLog(error.message);
     return handleErrorMessage(
         new IRISException("ActionExecutionIrisException", error.message)
     );
   }
 
   return result;
 }*/

  static async executeJSCode(value) {
    iLog(`executeJSCode`);
    let result = createResultObject();

    try {
      var scriptElement = document.createElement("script");
      scriptElement.type = "text/javascript";
      scriptElement.text = value;
      document.body.appendChild(scriptElement);

      new Function(value)();
      document.body.removeChild(scriptElement);

      result.success = true;
      result.message = "Script executed successfully.";
      result.isExist = true;
      result.text = result.message;
      result.value = true;
    } catch (error) {
      document.body.removeChild(scriptElement);
      return handleErrorMessage(
        new IRISException(
          "ActionExecutionIrisException",
          "Error executing the script: " + error.message+"\nStacktrace: "+error.stack
        )
      );
    }

    return result;
  }

  static async checkCodeJSValid(code) {
        // Vérifie la syntaxe sans exécuter le code, Sinon declencher une exception indiquant pourquoi le code n'est pas valid
        // TODO : à traiter
  }

  static async escapeBackticks(code)
  {
    const templateMatch = code.match(/`(.*)`/s);
    if (templateMatch) {
      const innerContent = templateMatch[1].replace(/`/g, '\\`');
      code = code.replace(templateMatch[1], innerContent);
    }
    
    const doubleQuoteMatches = code.match(/"([^"\\]*(?:\\.[^"\\]*)*)"/g);
    if (doubleQuoteMatches) {
      doubleQuoteMatches.forEach(match => {
        const inner = match.slice(1, -1).replace(/`/g, '\\`');
        code = code.replace(match, `"${inner}"`);
      });
    }
   return code;
  }

  static async executeJSCode(script, sendResponse) {
    iLog(`executeJSCode`);
    let result = createResultObject();
    try {

      //TODO: corriger le code de escapeBackticks avant de l'utiliser
      //script = script.replace(/\\/g, "\\\\");
      //script = await Executor.escapeBackticks(script);
      iLog(`here is your script ${script}.`);

      let scriptJs = `
        async function __executeIRISJS()
        {
          try { 
            ${script}
            sendResponseToIrisExtension("Script executed successfully.", true);
            isSuccess = true;
          } catch (error) {
            sendResponseToIrisExtension(error.message+" Stacktrace: "+error.stack, false);
          }
        }
        function sendResponseToIrisExtension(message, isSuccess) {
          window.postMessage({ type: "IRIS_JS_EXECUTOR", success: isSuccess, message }, "*");
        };
      (async () => {
      await __executeIRISJS();
    })();
        `;
      
      //Vérifier le script globale utilisateur+IRIS
      Executor.checkCodeJSValid(scriptJs);
      
      let irisDiv = document.createElement("irisDiv");

      irisDiv.setAttribute("onClick", scriptJs);

      document.body.appendChild(irisDiv);

      const clickEvent = new Event("click", { bubbles: false });
      irisDiv.dispatchEvent(clickEvent);

      setTimeout(() => irisDiv.remove(), 1000);
    } catch (error) {
      return handleErrorMessage(
        new IRISException(
          "ActionExecutionIrisException",
          error.message+"\nStacktrace: "+error.stack
        )
      );
    }
    window.addEventListener("message", (event) => {
      if (event.source !== window) return;
      
      if (event.data.type && event.data.type === "IRIS_JS_EXECUTOR") {
          result.success = event.data.success;
          result.message = event.data.message;
          result.isExist = true;
          result.value = true;
          result.text = result.message;

          sendResponse(
            !event.data.success ? handleErrorMessage(
            new IRISException(
              "ActionExecutionIrisException",
              event.data.message
            )
          ) : result)
      }
    });
  }

  static async simulateSelectRadio(xpath, value) {
    iLog(`simulateSelectRadio`);
    let result = createResultObject();

    try {
      var radioButton = selectSingleNode(xpath);

      if (radioButton) {
        if (!radioButton.checked) {
          radioButton.click();
          result.message = `Selected radio button with value: ${value}`;
        } else {
          result.message = `Selected radio button with value: ${value} is already selected`;
        }
        result.success = true;
        result.isExist = true;
        result.text = result.message;
        result.value = value;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static async simulateUnselectRadio(xpath, value) {
    iLog(`simulateSelectRadio`);
    let result = createResultObject();

    try {
      var radioButton = selectSingleNode(xpath);

      if (radioButton) {
        if (radioButton.checked) {
          radioButton.click();
          result.message = `Unselected radio button with value: ${value}`;
        } else {
          result.message = `The radio button with value: ${value} is already unselected`;
        }
        result.success = true;
        result.isExist = true;
        result.text = result.message;
        result.value = value;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static async simulateHover(xpath) {
    iLog(`simulateHover`);
    let result = createResultObject();
  
    try {
      // Find the element using XPath
      var targetElement = selectSingleNode(xpath);
  
      if (targetElement) {
        // Dispatch the mouseenter event to simulate hover
        const mouseEnterEvent = new MouseEvent('mouseenter', {
          bubbles: true,
          cancelable: true,
          view: window,
        });
        targetElement.dispatchEvent(mouseEnterEvent);
  
        // Optionally, you can also simulate a mouseover event
        const mouseOverEvent = new MouseEvent('mouseover', {
          bubbles: true,
          cancelable: true,
          view: window,
        });
        targetElement.dispatchEvent(mouseOverEvent);
  
        result.message = `Hover simulated successfully on element with XPath: ${xpath}`;
        result.success = true;
        result.isExist = true;
        result.text = result.message;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
  
    return result;
  }

  static async simulateFocus(xpath) {
    iLog(`simulateFocus`);
    let result = createResultObject();
  
    try {
      // Find the element using XPath
      var targetElement = selectSingleNode(xpath);
  
      if (targetElement) {
        // Focus on the element
        targetElement.focus();
  
        result.message = `Focus simulated successfully on element with XPath: ${xpath}`;
        result.success = true;
        result.isExist = true;
        result.text = result.message;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
  
    return result;
  }

  static async readFromInput(xpath) {
    iLog(`readFromInput`);
    let result = createResultObject();
    let value;

    try {
      const element = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ANY_TYPE,
        null
      );
      const node = element.iterateNext();

      if (node) {
        if (
          node.tagName.toLowerCase() === "input" ||
          node.tagName.toLowerCase() === "textarea"
        ) {
          value = node.value.trim();
        } else {
          value = node.textContent.trim();
        }

        iLog(`the retrieved value : ${value}`);

        result.success = true;
        result.message = "element was found";
        result.value = value;
        result.text = value;
      } else {
        throw new IRISException(
          "ActionExecutionIrisException",
          `Couldn't find element with XPath: ${xpath}`
        );
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async isElementVisible(xpath) {
    iLog(`isElementVisible`);
    let result = createResultObject();
    try {
      const iterator = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ANY_UNORDERED_NODE_TYPE,
        null
      );

      const element = iterator.singleNodeValue;

      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }

      const visible =
        !!element &&
        getComputedStyle(element).visibility !== "hidden" &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0;

      result.success = true;
      result.isExist = visible;
      result.value = visible;
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async getAttribute(xpath, attribute) {
    iLog(`getAttribute: ${attribute}`);
    let result = createResultObject();

    try {
      const iterator = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ANY_UNORDERED_NODE_TYPE,
        null
      );
      const element = iterator.singleNodeValue;

      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }

      const visible =
        !!element &&
        getComputedStyle(element).visibility !== "hidden" &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0;

      if (visible) {
        if (element.hasAttribute(attribute)) {
          result.success = true;
          result.isExist = true;
          result.value = element.getAttribute(attribute).toString();
        } else {
          result.success = false;
          result.isExist = false;
          result.value = null;
        }
      } else {
        result.success = false;
        result.isExist = false;
        result.value = null;
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async setAttribute(xpath, attribute, value) {
    iLog(`setAttribute: ${attribute} = ${value}`);
    let result = createResultObject();
  
    try {
      const iterator = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ANY_UNORDERED_NODE_TYPE,
        null
      );
      const element = iterator.singleNodeValue;
  
      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }
  
      const visible =
        !!element &&
        getComputedStyle(element).visibility !== "hidden" &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0;
  
      if (visible) {
        // Set the attribute
        element.setAttribute(attribute, value);
        result.success = true;
        result.isExist = true;
        result.value = value;
      } else {
        result.success = false;
        result.isExist = false;
        result.value = null;
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }
    return result;
  }

  static async getElementPosition(xpath) {
    iLog("getElementPosition");
    let result = createResultObject();

    try {
      const iterator = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ANY_UNORDERED_NODE_TYPE,
        null
      );
      const element = iterator.singleNodeValue;

      if (element) {
        element.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }

      const visible =
        !!element &&
        getComputedStyle(element).visibility !== "hidden" &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0;

      if (visible) {
        const rect = element.getBoundingClientRect();
        result.success = true;
        result.isExist = true;
        let position = {           
            x: rect.x,           
            y: rect.y        
          };        
        result.value = JSON.stringify(position);
      } else {
        result.success = false;
        result.isExist = false;
        result.value = null;
      }
    } catch (error) {
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message+"\nStacktrace: "+error.stack)
      );
    }

    return result;
  }

  static async getPageURL() {
    iLog("getPageURL");
    let result = createResultObject();

    result.success = true;
    result.isExist = true;
    result.value = window.location.href;

    return result;
  }

  static async getPageTitle() {
    iLog("getPageTitle");
    let result = createResultObject();

    result.success = true;
    result.isExist = true;
    result.value = document.title;

    return result;
  }

  static async executeWebScraping(request) {
    let result = createResultObject();
    const data = [];
    
    try {
      const elementsToScrape = request.elements;
      let count = request.count; 
      
      const pagination = request.pagination || {
        isPaginated: false,
        firstXPath: null,
        secondXPath: null
      };

      let currentPage = 1;
      let hasMorePages = true;

      for (let item of elementsToScrape)
      {
        let xpathsElementsInfo = this.extractXpathInfo(item.firstXPath, item.secondXPath);
        //completer les données de 'item par celles du xpathInfo 
        Object.assign(item, xpathsElementsInfo);
      }

      let xpathsPaginationInfo = null;
      if (pagination.isPaginated) {
        xpathsPaginationInfo = this.extractXpathInfo(pagination.firstXPath, pagination.secondXPath);
      }
      
      while (hasMorePages && (count === 0 || data.length < count)) {
        for (let dynamicIndex = 0; ; dynamicIndex++) {
          const rowData = { id: data.length + 1 };
          let pageDataFound = false;

          for (let item of elementsToScrape) {

            const elementName = item.elementName;     
            const dynamicXpath = this.findDynamicXpath(item, dynamicIndex);
            const dataValue = this.extractTextFromXPath(dynamicXpath, item.attribute);            
            if (dataValue !== null) {
              rowData[elementName] = dataValue;
              pageDataFound = true;
            } else {
              pageDataFound = pageDataFound || false;
              rowData[elementName] = "";
            }
          }
          
          if (!pageDataFound) {
            break;
          }
          data.push(rowData);

          if (count > 0 && data.length >= count) {
            hasMorePages = false;
            break;
          }
        }
        
        if (pagination.isPaginated && data.length < count) 
        {
          const nextPageXPath = this.findDynamicXpath(xpathsPaginationInfo, currentPage++);

          const nextPageElement = document.evaluate(
            nextPageXPath,
            document,
            null,
            XPathResult.FIRST_ORDERED_NODE_TYPE,
            null
          ).singleNodeValue;
          if (!nextPageElement) {
            hasMorePages = false;
            break;
          }
          nextPageElement.click();
          await new Promise((resolve) => {
            setTimeout(() => {
              resolve();
            }, 5000);
          });

        } else {
          hasMorePages = false;
        }
      }

      result.success = true;
      result.isExist = true;
      result.text = result.message;
      result.message = `element scraping`;
      result.value = JSON.stringify(data);

    } catch (error) {
      console.log("Error during scraping process: ", error);
      result.success = false;
      return handleErrorMessage(
        new IRISException("ActionExecutionIrisException", error.message)
      );
    }
    return result;
  }

  //returner les element pour construire un xpath dynamique de type : 
  // preXpath + [ position du l'element: à caclulé à base de distanceElement et indexFirstElementToChange] + postXpath
  static extractXpathInfo(xpath1, xpath2) 
  {
    
    let isSameElement = false;
    let preXpath = "";
    let postXpath = "";
    let distanceElement = 0;
    let idexFirstElement = 0;
    let idexSecondElement = 0;

    //Cas d'une pagination avec le meme button (next page), donc meme xpath
    if (xpath1?.trim() == xpath2?.trim()) 
    {
      isSameElement = true;
      //Supprimer le dernier / s'il existe dans l'xpath
      preXpath =  xpath1.replace(/\/$/, '');
    }
    else
    {

      // Split sur les chiffres, en conservant les chiffres, et filtre les valeurs vides
      let listSigmentXpath1 = xpath1.split(/(\d+)/).filter(Boolean);
      let listSigmentXpath2 = xpath2.split(/(\d+)/).filter(Boolean);

      let minLength = Math.min(listSigmentXpath1.length, listSigmentXpath2.length);
  
      //trouver le preXpath (partie commun entre xpath1 et xpath2 avant l'index qui chanque) : exemple "table[1]/tbody/tr[text()='ok' and "
      let i = 0;
      for (; i < minLength; i++) {
          if (listSigmentXpath1[i] === listSigmentXpath2[i]) {
            preXpath += listSigmentXpath1[i];
          } else {
              break;
          }
      }

      //récupérer les indexes de differnces entre les deux Xpath
      idexFirstElement  =  parseInt(listSigmentXpath1[i]);
      idexSecondElement  = parseInt(listSigmentXpath2[i]);


      // Calcul de la distance (le pas) entre la positions (les indexes) des deux elements répresentés par les deux xpath : 2-1 = 1 dans l'exemple
      distanceElement = idexSecondElement - idexFirstElement;


      i++;
      //trouver le postXpath le reste de la chaine.
      for (; i < listSigmentXpath1.length; i++) {
        postXpath += listSigmentXpath1[i];
      }

    }
    return {
      "isSameElement" : isSameElement,
      "preXpath" : preXpath,
      "postXpath" : postXpath,
      "idexFirstElement" : idexFirstElement ,
      "distanceElement" : distanceElement,
    };
  }

  static findDynamicXpath(xpathInfo, dynamicIndex) 
  {
    //Cas d'une pagination avec le meme button (next page), donc meme xpath
    if(xpathInfo.isSameElement)
    {
      return xpathInfo.preXpath;
    }
    const indexCurrentElement = xpathInfo.idexFirstElement +  dynamicIndex *  xpathInfo.distanceElement; 
    //Calcul du dynamique Xpath? Exemple  "//table[@id="id_tab"]/tbody/tr[" + 2 + "]/td[4]" ==>  //table[@id="id_tab"]/tbody/tr[2]/td[4]       
    const dynamicXpath = xpathInfo.preXpath + indexCurrentElement + xpathInfo.postXpath;
    return dynamicXpath;
  }

 /* static findDynamicXpath(xpath1, xpath2, dynamicIndex) {
    let arr1 = xpath1.split("/");
    let arr2 = xpath2.split("/");

    let i = 0;
    while (i < arr1.length && i < arr2.length && arr1[i] === arr2[i]) {
      i++;
    }

    let dynamicXpath = arr1.slice(0, i).join("/") + "/";
    dynamicXpath +=
      arr1[i].split("[")[0] +
      "[" +
      (dynamicIndex +
        parseInt(arr2[i].split("[")[1]) -
        parseInt(arr1[i].split("[")[1])) +
      "]";
    dynamicXpath += "/" + arr2.slice(i + 1).join("/");

    return dynamicXpath.charAt(dynamicXpath.length - 1) === "/" ? dynamicXpath.slice(0, dynamicXpath.length - 1) : dynamicXpath;
  }*/

  static extractTextFromXPath(xpath, attribute) {
    if (!xpath) return null;
    const result = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
    ).singleNodeValue;

    if (result) {
          // Retourner directement l'XPath si l'attribut demandé est "xpath"
        if (attribute?.toLowerCase() === "xpath") {
            
            return xpath;
        }
        // Retourner l'attribut spécifié si demandé, sinon le texte
        return attribute ? result.getAttribute(attribute) : result.textContent.trim();
    }

    return null;
}
}
