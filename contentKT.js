
iLog('IRIS KT plugin loaded');
let connectKT = null;
const DOUBLE_CLICK_DELAY = 400;

const KTAction = {
    LEFT_CLICK: {action: "LeftClick", iconPath: "/images/skills/LeftClick.svg"},
    RIGHT_CLICK: {action: "RightClick", iconPath: "/images/skills/RightClick.svg"},
    DOUBLE_CLICK: {action: "DoubleClick", iconPath: "/images/skills/DoubleClick.svg"},
    CHECKBOX: {action: "CheckBox", iconPath: "/images/skills/CheckBox.svg"},
    UNCHECKBOX: {action: "UncheckBox", iconPath: "/images/skills/UnchechBox.svg"},
    SELECT_RADIO: {action: "SelectRadio", iconPath: "/images/skills/SelectRadio.svg"},
    UNSELECT_RADIO: {action: "UnSelectRadio", iconPath: "/images/skills/UnSelectRadio.svg"},
    TYPE_INTO: {action: "TypeInto", iconPath: "/images/skills/TypeInto.svg"},
    READ: {action: "Read", iconPath: "/images/skills/Read.svg"},
    SELECT_VALUE: {action: "SelectValue", iconPath: "/images/skills/SelectValue.svg"},
    PRESS_KEY: {action: "PressKey", iconPath: "/images/skills/PressKey.svg"},
    PRESS_ENTER: {action: "PressEnter", iconPath: "/images/skills/PressEnter.svg"},
    PRESS_TAB: {action: "PressTab", iconPath: "/images/skills/PressTab.svg"},
    PRESS_DELETE: {action: "PressDelete", iconPath: "/images/skills/PressDelete.svg"},
    PRESS_HOVER: {action: "Hover", iconPath: "/images/skills/WebHover.svg"} //TODO: hover n'est prise en compte.
};

class KnowledgeTransfer {
    constructor() {
        this._KTStarted = false;
        this.lastMouseDownTime = 0;
        this.currentMouseDownTime = 0;

        // Bind methods in the constructor
        this.handleChange = this.handleChange.bind(this);
        this.handleBlur = this.handleBlur.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleCopy = this.handleCopy.bind(this);
        this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
    }

    registerEventHandlers() {
        // iLog(`registerEventHandlers`);
        let result = createResultObject();

        if (!this._KTStarted) {
            iLog("Add Event Listeners");
            document.addEventListener("mousedown", this.handleMouseDown);
            document.addEventListener("mouseup", this.handleMouseUp);
            document.addEventListener("change", this.handleChange);
            document.addEventListener("blur", this.handleBlur, true);
            document.addEventListener("keydown", this.handleKeyDown, true);
            document.addEventListener("copy", this.handleCopy);
            window.addEventListener("beforeunload", this.handleBeforeUnload);

            this._KTStarted = true;

            result.message = "KT started successfully";
            result.text = result.message;
            result.success = true;
            return result;
        } else {
            iLog(`KT already started`);
            throw new IRISException(
                `ActionExecutionIrisException`,
                `KT already started`
            );
        }
    }

    unregisterEventHandlers() {
        let result = createResultObject();

        if (this._KTStarted) {
            iLog("Remove Event Listeners");

            document.removeEventListener("mousedown", this.handleMouseDown);
            document.removeEventListener("mouseup", this.handleMouseUp);
            document.removeEventListener("change", this.handleChange);
            document.removeEventListener("blur", this.handleBlur, true);
            document.removeEventListener("keydown", this.handleKeyDown, true);
            document.removeEventListener("copy", this.handleCopy);
            window.removeEventListener("beforeunload", this.handleBeforeUnload);

            this._KTStarted = false;

            result.message = "KT stopped successfully";
            result.text = result.message;
            result.success = true;

            return result;
        } else {
            iLog(`KT already stopped`);
            throw new IRISException(
                `ActionExecutionIrisException`,
                `KT already stopped`
            );
        }
    }

    static sendRecordedStep(step) {
        chrome.runtime.sendMessage({
            action: "sendRecordedStep",
            message: step
        });
    }

    static getXPath(element) {

        let xpath = "";
        // Boucle pour remonter l'arbre DOM (1 signifie une base et pas un commentaire ou attribut, ...)
        while (element && element.nodeType === 1)
        {
            
            //Si l'Id existe et ne contient pas des chiffres (cas des id dynamiques)
            if (element.id && !/\d/.test(element.id)) 
            {

                let elementTagName = element.tagName.toLowerCase();
                let xpathId = `//${elementTagName}[@id="${element.id}"]`;

                //L'id n'est pas unique dans certains site web qui ne respectent pas les bonnes pratiques. on retourtne sa position "xpathId[position]"
                let xpathIdWithPositionElement = KnowledgeTransfer.updateXpathPositionOfElement(element, xpathId);

                xpath = `${xpathIdWithPositionElement}${xpath}`;

                break;
            }
        
            // Sinon, construire un segment basé sur le tag et l'index
            xpath = KnowledgeTransfer.getElementNameWithPosition(element)+xpath;

            // Passer à l'élément parent
            element = element.parentNode;
        }
        return xpath;     
    }

    static getElementNameWithPosition(element) {

        let elementTagName = element.tagName.toLowerCase();
        let index = 1; // Les index XPath commencent à 1
        let sibling = element.previousSibling;
        let isUniqueElement  = true;
        while (sibling)
        {
          if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
            index++;
            isUniqueElement = false;
          }
          sibling = sibling.previousSibling;
        }

        sibling = element.nextSibling;
        while (sibling) 
        {
            if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
              isUniqueElement = false;
              break;
            }
            sibling = sibling.nextSibling;
          }
        // si l'element est unique pas besoin d'ajouter l'index "elementTagName[index]"
        if(index == 1  && isUniqueElement)
        {
            return `/${elementTagName}`; 
        }
        else
        {
            return `/${elementTagName}[${index}]`; 
        }
      }


    // retourne l'index de l'element dans la page html
    static updateXpathPositionOfElement(element, xpath) {
        // Récupérer tous les éléments correspondant au XPath
        const xpathResult = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
        let elementPosition = 1;
        // Parcourir les résultats pour trouver l'index de l'élément
        for (let i = 0; i < xpathResult.snapshotLength; i++) {
          if (xpathResult.snapshotItem(i) === element) {
            elementPosition = i +1; // Les index XPath commencent à 1
            break;
          }
        }
        // si l'element est unique dans la page on n'ajoute pas [1]
        if(elementPosition == 1 && xpathResult.snapshotLength == 1)
        {
            return xpath;
        }
        else
        {
            return `(${xpath})[${elementPosition}]`; 
        }
      }
      

    getParentOrSelf(element) {
        return element.parentNode || element;
    }

    captureDivAsBase64(element) {
        return new Promise((resolve, reject) => {
            if (!element) {
                reject(new Error("Element is not provided"));
                return;
            }

            html2canvas(element, { useCORS: true })
            .then((canvas) => {
                // Créer un contexte 2D avec willReadFrequently pour optimiser les opérations de lecture
                const optimizedCanvas = document.createElement("canvas");
                optimizedCanvas.width = canvas.width;
                optimizedCanvas.height = canvas.height;
                const ctx = optimizedCanvas.getContext("2d", { willReadFrequently: true });
        
                // Copier le contenu du canvas original dans le nouveau canvas
                ctx.drawImage(canvas, 0, 0);
        
                // Convertir le canvas optimisé en base64
                const base64Image = optimizedCanvas.toDataURL("image/png");
                resolve(base64Image);
            })
            .catch((error) => {
                reject(error);
            });
        
        });
    }

    generateStepName(element, action, value) {
        const elementTagName = element.tagName.toLowerCase();

        const getLabel = () => {
            if (element.getAttribute('aria-label')) return element.getAttribute('aria-label');
            if (element.id) {
                const associatedLabel = document.querySelector(`label[for="${element.id}"]`);
                if (associatedLabel) return associatedLabel.textContent.trim();
            }
            if (element.closest('label')) return element.closest('label').textContent.trim();
            const previousLabel = element.previousElementSibling;
            if (previousLabel && previousLabel.tagName.toLowerCase() === 'label') {
                return previousLabel.textContent.trim();
            }
            if (element.placeholder) return element.placeholder.trim();
            if (elementTagName === 'button' || (elementTagName === 'input' && element.type === 'button')) {
                return element.textContent.trim() || element.value.trim();
            }
            if (element.textContent.trim()) return element.textContent.trim();
            return elementTagName;
        };

        let label = getLabel();
        label = label.length > 20 ? `${label.substring(0, 20)}..` : label;

        let actionDescription;
        switch (action) {
            case KTAction.LEFT_CLICK:
                actionDescription = `Click on ${label}`;
                break;
            case KTAction.RIGHT_CLICK:
                actionDescription = `Right-click on ${label}`;
                break;
            case KTAction.DOUBLE_CLICK:
                actionDescription = `Double-click on ${label}`;
                break;
            case KTAction.TYPE_INTO:
                actionDescription = `Write in ${label}`;
                break;
            case KTAction.READ:
                actionDescription = `Read ${label}`;
                break;
            case KTAction.CHECKBOX:
                actionDescription = `${element.checked ? 'Check' : 'Uncheck'} ${label}`;
                break;
            case KTAction.UNCHECKBOX:
                actionDescription = `Uncheck ${label}`;
                break;
            case KTAction.SELECT_RADIO:
                actionDescription = `Select ${label}`;
                break;
            case KTAction.UNSELECT_RADIO:
                actionDescription = `Deselect ${label}`;
                break;
            case KTAction.SELECT_VALUE:
                actionDescription = `Select value from ${label}`;
                break;
            case KTAction.PRESS_KEY:
            case KTAction.PRESS_ENTER:
            case KTAction.PRESS_TAB:
            case KTAction.PRESS_DELETE:
                actionDescription = `Press ${value} on ${label}`;
                break;
            default:
                actionDescription = `${action.toLowerCase().replace('_', ' ')} ${label}`;
        }

        return actionDescription.charAt(0).toUpperCase() + actionDescription.slice(1);
    }

    //Récuéper le nom de l'app web depuis l'url
   getApplicationWebName() {
    const host = window.location.hostname.replace("www.", ""); // Récupère le hostname (ex: "ab.cd.ef.com")
    const parts = host.split('.'); // Divise le hostname en parties selon les points

    // Ignore le dernier élément s'il correspond à un TLD comme "com", "io", etc.
    const filteredParts = parts.slice(0, -1);

    // Transforme chaque partie après la première en capitalisant la première lettre
    const formattedName = filteredParts
        .map((part, index) => 
            index === 0 ? part : part[0].toUpperCase() + part.slice(1)
        )
        .join(''); // Recombine les parties en une seule chaîne

    return formattedName+"WebApp";;

    }
    

    handleActionTypeEvent(event, ktAction, value = "") {
        const element = event.target;
        //let result = Executor.highlightElement(KnowledgeTransfer.getXPath(element));
        
        const step = {
            id: Date.now().toString(),
            destination: "IRIExecuterKT",
            name: this.generateStepName(element, ktAction, value),
            skillName: "Web."+ktAction.action, 
            description: "",
            type: "Web",
            parameters: {
                function: ktAction.action, 
                xpath: KnowledgeTransfer.getXPath(element),
                value: value || element.value,
                instance: this.getApplicationWebName()
              },
            style: {
              position: {
                x: 0,
                y: 0
              },
              icon: ktAction.iconPath,
              children: [{ id: "end" }]
            },
            timeout: 60,
            nextStepId: ""
        };

    /*   const step = {
            id: Date.now().toString(),
            destination: "kt",
            name: this.generateStepName(element, action, value),
            description: "",
            type: "Web",
            parameters: {
                actionType: action,
                xpath: KnowledgeTransfer.getXPath(element),
                value: value || element.value,
                tabId: null, // It will be set by the service worker
                prompt: "",
                screenShot: ""
            },
            nextStepId: "",
            timeout: 60,
            style: {
                position: {
                    x: 0,
                    y: 0
                },
                children: [{ id: "end" }]
            }
        };*/ 

        const capturePromise = this.captureDivAsBase64(
            this.getParentOrSelf(element)
        )
            .then((base64Image) => {
                step.parameters.screenShot = base64Image;
            })
            .catch((error) => {
                console.log("Error capturing screenshot:", error);
                step.parameters.screenShot = "";
            });

        capturePromise.then(() => {
            KnowledgeTransfer.sendRecordedStep(step);
        });
    }

    handleMouseDown(event) {
        if (event.button === 0) {
            this.lastMouseDownTime = this.currentMouseDownTime;
            this.currentMouseDownTime = Date.now();
        }
    }

    handleMouseUp(event) {
        let actionType;

        const isIgnoredElement = (element) => {
            const ignoredTypes = ["radio", "checkbox", "select"];
            return (
                ignoredTypes.includes(element.type) ||
                (element.tagName === "INPUT" &&
                    ignoredTypes.includes(element.firstChild?.type)) ||
                (element.tagName === "LABEL" &&
                    ignoredTypes.includes(element.firstChild?.type))
            );
        };

        if (!isIgnoredElement(event.target)) {
            if (event.button === 0) {
                const timeSinceLastMouseDown =
                    this.currentMouseDownTime - this.lastMouseDownTime;

                if (timeSinceLastMouseDown < DOUBLE_CLICK_DELAY) {
                    //Double Click
                    actionType = this.handleDoubleClick(event);
                    this.handleMouseEventResponse(event, actionType);
                } else {
                    //Click Left
                    this.handleLeftClick(event).then((result) => {
                        actionType = result;
                        this.handleMouseEventResponse(event, actionType);
                    });
                }
            } else if (event.button === 2) {
                //Click Right
                actionType = this.handleRightClick(event);
                this.handleMouseEventResponse(event, actionType);
            }
        }

        iLog(`Other element was detected`);
    }

    handleDoubleClick(event) {
        iLog("Double click detected");
        return KTAction.DOUBLE_CLICK;
    }

    handleRightClick(event) {
        iLog("Right click detected");
        event.preventDefault(); //prevent ContextMenu
        return KTAction.RIGHT_CLICK;
    }

    handleLeftClick(event) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // Check if there was no double click during the delay
                if (Date.now() - this.currentMouseDownTime >= DOUBLE_CLICK_DELAY) {
                    resolve(KTAction.LEFT_CLICK);
                } else {
                    resolve("");
                }
            }, DOUBLE_CLICK_DELAY);
        });
    }

    handleMouseEventResponse(event, actionType) {
        if (actionType) {
            iLog(`Mouse action: ${actionType} was captured`);
            this.handleActionTypeEvent(event, actionType);
        }
    }

    handleBlur(event) {
        if (
            (event.target.type === "text" ||
                event.target.type === "password" ||
                event.target.type === "textarea") &&
            event.target.tagName !== "SELECT" &&
            event.target.value !== ""
        ) {
            iLog(`blur event: ${event}`);
            this.handleActionTypeEvent(event, KTAction.TYPE_INTO);
        }
    }

    handleKeyDown(event) {
        iLog(`keypress event: ${event}`);
        var actionKey = "";
        if (event.key === "Enter") {
            if (event.target.tagName.toLowerCase() === "textarea") {
                return;
            }
            actionKey = KTAction.PRESS_ENTER;
        } else if (event.key === "Tab") {
            actionKey = KTAction.PRESS_TAB;
        }
        if (actionKey) {
            setTimeout(() => {
                this.handleActionTypeEvent(event, actionKey);
            }, 1000);
        }
    }

    handleChange(event) {
        iLog(`change event: ${event}`);
        if (event.target.tagName === "SELECT") {
            this.handleActionTypeEvent(event, KTAction.SELECT_VALUE);
        } else if (
            event.target.tagName === "INPUT" &&
            event.target.type === "checkbox"
        ) {
            if (event.target.checked) {
                this.handleActionTypeEvent(event, KTAction.CHECKBOX);
            } else {
                this.handleActionTypeEvent(event, KTAction.UNCHECKBOX);
            }
        } else if (
            event.target.tagName === "INPUT" &&
            event.target.type === "radio"
        ) {
            if (event.target.checked) {
                this.handleActionTypeEvent(event, KTAction.SELECT_RADIO);
            } else {
                this.handleActionTypeEvent(event, KTAction.UNSELECT_RADIO);
            }
        }
    }

    handleCopy(event) {
        iLog(`copy event: ${event}`);
        let readValue = "";
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            readValue = event.target.value;
        } else {
            readValue = window.getSelection().toString();
        }
        this.handleActionTypeEvent(event, KTAction.READ, readValue);
    }

    handleBeforeUnload(event) {
        const confirmationMessage = "KT: You're leaving this page. Proceed?";
        (event || window.event).returnValue = confirmationMessage;
        return confirmationMessage;
    }
}

function createResultObject() {
    return {
        text: "",
        value: "",
        isExist: false,
        success: false,
        message: "",
        timeout: 0,
    };
}

function iLog(...args) { console.log(`IRIS_KT_CONTENT :: ${ JSON.stringify(args)}`); }

function handleErrorMessage(error) {
    let result = createResultObject();

    if (error instanceof IRISException) result.message = error.message;
    else result.message = `Unexpected error: ${error.message}`;

    result.success = false;
    result.text = result.message;
    result.value = result.message;
    iLog("handleErrorMessage : ", result);
    return result;
}

function createKTInstance() {
    if (!connectKT) {
        connectKT = new KnowledgeTransfer();
    }
    return connectKT;
}

async function startKT() {
    connectKT = createKTInstance();
    iLog("launchKT ");
    try {
        return await connectKT.registerEventHandlers();
    } catch (error) {
        return handleErrorMessage(error);
    }
}

async function stopKT() {
    connectKT = createKTInstance();
    try {
        return await connectKT.unregisterEventHandlers();
    } catch (error) {
        return handleErrorMessage(error);
    }
}

/* Listeners between Service Worker and Content
 *
 */
chrome.runtime.sendMessage({
    action: "logFromContentScript",
    message: "successfully connected with IRIS_CONTENT",
});

/*
chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {

    console.log('Content script received a message:', request);

    let action = request.action.toUpperCase();

    iLog(`action selected: ${action}`);

    switch (action) {
        case "STARTKT":
            (async () => {
                let result = await startKT();
                sendResponse(result);
            })();
            return true;

        case "STOPKT":
            (async () => {
                let result = await stopKT();
                sendResponse(result);
            })();
            return true;

        default:
            console.log("Unknown action:", action);
            break;
    }
    return true;
});*/