iLog('IRIS Spy and Highlight Element (SHE) plugin loaded');
let spyAndHighlightElement = null;

// 10 min max d'attente pour spayer un element
const TIMEOUT_LIMIT_SHE_MIN =  10;

class SpyAndHighlightElement {
  constructor() {
      this.style = null;
      this.highlightRect = null;
      this.currentElement = null;
      this.timeoutId = null;
      this.stopSpyElementPromise = null;
      this.response = createResultObject();
      this.menuSpyElement = new IrisSpyContextMenu(this);
      this.currentXpath = "";
     }

  async startSpyElement() {
      iLog(`startSpyElement`);
      try {
            this.response = createResultObject();
            /*this.response.success = true;
            this.response.isExist = true;
            this.response.value = "";
            //Si la page HTML contient déja le div de Spy (id = iris-highlight), on ne relance pas le Spy une 2eme fois.
            if(document.getElementById("iris-highlight"))
            {
                this.response.message = `The spy operation is already running.`;
                this.response.text = this.response.message;
                return this.response;
            }*/
            this.ensureHighlightRect();
            this.menuSpyElement.addMenuToPage();
            this.mousemoveListener = this.mousemoveListener.bind(this);
            this.keydownListener = this.keydownListener.bind(this);
            document.addEventListener('mousemove', this.mousemoveListener);
            document.addEventListener('keydown', this.keydownListener);
            // Attente un délai TIMEOUT_LIMIT_SHE_MIN, si la méthode stopSpyElement n'est pas appelée, on arrête automatiquement le spy
            this.stopSpyElementPromise = new Promise((resolve) => {
                this.resolveStopSpyElementPromise = resolve;
                this.timeoutId = setTimeout(() => {
                    this.response = createResultObject();
                    this.response.success = false;
                    this.response.isExist = false;
                    this.response.message = `The spy operation was canceled due to a timeout! Please complete the element spying within ${TIMEOUT_LIMIT_SHE_MIN} minutes.`;
                    this.response.text = this.response.message;
                    this.response.value = "";
                    this.stopSpyElement();
                    resolve();
                }, TIMEOUT_LIMIT_SHE_MIN * 60 * 1000);
            });

            //Attendre l'appelle de la méthode stopSpyElement qui indique la fin du spy
            await this.stopSpyElementPromise;
            
        } catch (error) {
            iLog(error.message);
            this.response =  handleErrorMessage(
                new IRISException("SpyElementException", error.message)
            );
          }
          
   /* this.response.message = `The spy operation is started.`;
    this.response.text = this.response.message;*/
    return this.response;
  }

  async stopSpyElement() {
      iLog(`stopSpyElement`);
      let result = createResultObject();
      try {
            clearTimeout(this.timeoutId); // Annuler le délai d'arrêt automatique
            document.removeEventListener('mousemove', this.mousemoveListener);
            document.removeEventListener('keydown', this.keydownListener);
            if (this.highlightRect) {
                this.highlightRect.remove();
                this.highlightRect= null;
            }
            if (this.style) {
                this.style.remove();
                this.style = null;
            }
            this.currentElement = null;

            //Informer la méthode startSpyElement que le stop est appelé
            if (this.resolveStopSpyElementPromise) {
                this.resolveStopSpyElementPromise();
            }

            this.menuSpyElement.deleteMenuFromPage();

            result.success = true;
            result.isExist = true;
            result.value = true;
            result.message = "stop Spy Element succes";
            result.text = false;

        } catch (error) {
            iLog(error.message);
            result =  handleErrorMessage(
                new IRISException("StopSpyElementException", error.message)
            );
          }
        
          return result;
  }

  async highlightElement(xpath) {
    iLog(`highlightElement`);
    let result = createResultObject();
    try {
          this.ensureHighlightRect();
          const element = this.getElementByXPath(xpath);
          if (element) {
              this.updateHighlightRect(element);
              await this.blinkHighlightRect(element);
              result.success = true;
              result.isExist = true;
              result.value = true;
              result.message = "";
              result.text = false;
          }
          else
          {
            throw new IRISException("HighlightElementException", `Element was not found '${xpath}'`);
          }
      } catch (error) {
          iLog(error.message);
          result =  handleErrorMessage(
              new IRISException("HighlightElementException", error.message)
          );
        }
      
        return result;
}


  ensureHighlightRect() {
    this.style = document.createElement('style');
    this.style.innerHTML = `
        #iris-highlight {
            position: fixed;
            pointer-events: none;
            border: 2px solid blue;
            background-color: rgba(0, 0, 255, 0.1);
            z-index: 99999996;
        } 
    `;
      document.head.appendChild(this.style);
       if (!this.highlightRect) {
          this.highlightRect = document.createElement('div');
          this.highlightRect.id = 'iris-highlight';
          document.body.appendChild(this.highlightRect);
      }
  }

  mousemoveListener(event) {
      const elementUnderCursor = document.elementFromPoint(event.clientX, event.clientY);
      //Spyer tous, sauf le menu d'iris et le highlight
      if (elementUnderCursor && elementUnderCursor !== this.highlightRect && !this.menuSpyElement.existInMenu(elementUnderCursor)) {
          this.updateHighlightRect(elementUnderCursor);
      }
  }

  sendSpyElementRessponse(response) {
    chrome.runtime.sendMessage({
      action: "spyElementResponse",
      response: response,
    });
  }
  
  /**
 * Gets comprehensive information about a DOM element
 * @param {HTMLElement} element - The DOM element to analyze
 * @returns {Object|null} Detailed element information or null if element is invalid
 */
 getElementInfo(element) {
    if (!element || !(element instanceof HTMLElement)) return null;

    try {
        const position = this.getElementPosition(element);
        const siblings = this.getSiblingsInfo(element);

        const info = {
            tagName: element.tagName.toLowerCase(),
            textContent: element.textContent ? element.textContent.trim() : '',
            xpath: KnowledgeTransfer.getXPath(element),
            fullXPath: this.getFullXPath(element)
        };

        // Add all attributes as individual properties
        for (const attr of element.attributes) {
            info[attr.name] = attr.value;
        }

        return info;
    } catch (error) {
        console.error('Error getting element info:', error);
        return null;
    }
}

// Add this new helper method to get full XPath
getFullXPath(element) {
    if (!element) return '';
    if (element.nodeType === Node.DOCUMENT_NODE) return '';

    let path = '';
    while (element && element.nodeType === Node.ELEMENT_NODE) {
        let name = element.nodeName.toLowerCase();
        let index = 1;
        let sibling = element.previousSibling;
        
        while (sibling) {
            if (sibling.nodeType === Node.ELEMENT_NODE && 
                sibling.nodeName.toLowerCase() === name) {
                index++;
            }
            sibling = sibling.previousSibling;
        }
        
        const pathIndex = index > 1 ? `[${index}]` : '';
        path = `/${name}${pathIndex}${path}`;
        element = element.parentNode;
    }
    
    return path;
}
  
  /**
   * Gets the position of an element relative to its siblings
   * @param {HTMLElement} element - The element to analyze
   * @returns {Object} Position information
   */
   getElementPosition(element) {
    const siblings = Array.from(element.parentElement?.children || []);
    const index = siblings.indexOf(element);
    return {
      index: index + 1,
      totalSiblings: siblings.length
    };
  }
  
  /**
   * Gets information about the parent element
   * @param {HTMLElement} element - The element to analyze
   * @returns {Object|null} Parent element information
   */
   getParentInfo(element) {
    const parent = element.parentElement;
    if (!parent) return null;
  
    return {
      tagName: parent.tagName.toLowerCase(),
      id: parent.id || '',
      className: parent.className || '',
      role: parent.getAttribute('role') || ''
    };
  }
  
  /**
   * Gets information about sibling elements
   * @param {HTMLElement} element - The element to analyze
   * @returns {Array} Information about siblings
   */
   getSiblingsInfo(element) {
    const siblings = Array.from(element.parentElement?.children || []);
    return siblings.map(sibling => ({
      tagName: sibling.tagName.toLowerCase(),
      id: sibling.id || '',
      className: sibling.className || '',
      isTarget: sibling === element
    }));
  }
  
  /**
   * Gets the match index of an element when using an XPath
   * @param {HTMLElement} element - The target element
   * @param {string} xpath - The XPath to evaluate
   * @returns {number} The 1-based index of the element in the XPath matches, or -1 if not found
   */
   getMatchIndex(element, xpath) {
    try {
      const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
      for (let i = 0; i < result.snapshotLength; i++) {
        if (result.snapshotItem(i) === element) {
          return i + 1; // Return 1-based index
        }
      }
      return -1;
    } catch (error) {
      console.error('Error getting match index:', error);
      return -1;
    }
  }
  
  
  
  doAdvancedSpy() {
    try {
        if (!this.currentElement) {
            return;
        }

        // Get detailed element info
        const elementInfo = this.getElementInfo(this.currentElement);
        if (!elementInfo) {
            return;
        }

        createResultObject();
        this.response.success = true;
        this.response.isExist = true;
        this.response.value = JSON.stringify(elementInfo);
        this.response.message = `Advanced spy completed for element: ${elementInfo.tagName}`;
        this.response.text = this.response.message;
    } finally {
        this.stopSpyElement();
    }
}


  doSpyElement(){
    if(!this.currentXpath && this.currentElement)
    {
        this.currentXpath = KnowledgeTransfer.getXPath(this.currentElement);
    }
    if (this.currentXpath) {

        this.response = createResultObject();
        this.response.success = true;
        this.response.isExist = true;
        this.response.message = `Selected xpath with value: ${this.currentXpath}`;
        this.response.text = this.response.message;
        this.response.value = this.currentXpath;
        this.stopSpyElement();
        //this.sendSpyElementRessponse(this.response);
    }
  }  

  doCancelSpy()
  {
    this.response = createResultObject();
    this.response.success = true;
    this.response.isExist = true;
    this.response.message = `The Spy operation is canceled by the user`;
    this.response.text = this.response.message;
    this.response.value = "";
    this.stopSpyElement();
    //this.sendSpyElementRessponse(this.response);
  }

  keydownListener(event) {
      // Echape pour annuler le spy
      if (event.key.toLowerCase() === 'escape') {
        this.doCancelSpy();   
      }
  }

  updateHighlightRect(target) {
      const rect = target.getBoundingClientRect();
      this.highlightRect.style.width = `${rect.width}px`;
      this.highlightRect.style.height = `${rect.height}px`;
      this.highlightRect.style.left = `${rect.left + window.scrollX}px`;
      this.highlightRect.style.top = `${rect.top}px`;
      if(this.currentElement != target)
      {
        this.currentElement = target;
        this.currentXpath = KnowledgeTransfer.getXPath(this.currentElement);
        this.menuSpyElement.hideContextMenu();
      }
      
  }

  getElementByXPath(xpath) {
      return document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
  }

  async blinkHighlightRect(element) {
    let isBlue = true;

    // Mise en place des intervalles pour les mises à jour
    const intervalUpdate = setInterval(() => {
        this.updateHighlightRect(element);
    }, 50);

    const interval = setInterval(() => {
        this.highlightRect.style.borderColor = isBlue ? 'red' : 'blue';
        isBlue = !isBlue;
    }, 500);

    // Promesse qui attend 5 secondes avant de nettoyer et de terminer
    return new Promise((resolve) => {
        setTimeout(() => {
            clearInterval(interval);
            clearInterval(intervalUpdate);
            if (this.highlightRect) {
                this.highlightRect.remove();
                this.highlightRect = null;
            }
            resolve(); // Résolution de la promesse après le nettoyage
        }, 5000);
    });
}


}

function createResultObject() {
    return {
        text: "",
        value: "",
        isExist: false,
        success: false,
        message: "",
        timeout: 0,
    };
}

function iLog(...args) { console.log(`IRIS_SHE_CONTENT :: ${ JSON.stringify(args)}`); }

function handleErrorMessage(error) {
    let result = createResultObject();

    if (error instanceof IRISException) result.message = error.message;
    else result.message = `Unexpected error: ${error.message}`;

    result.success = false;
    result.text = result.message;
    result.value = result.message;
    iLog("handleErrorMessage : ", result);
    return result;
}

function getSpyAndHighlightElement() {
    return spyAndHighlightElement;
}

function createSpyAndHighlightElement() {
  spyAndHighlightElement = new SpyAndHighlightElement();
  return spyAndHighlightElement;
}

//Class pour la gestion du menu (spy element et cancel) suite au click droit
class IrisSpyContextMenu {
    constructor(pSpyAndHighlightElement) {
        this.contextMenu = null;
        this.style = null;
        this.spyAndHighlightElement = pSpyAndHighlightElement;
    }

    // Méthode d'initialisation pour créer les éléments du menu
    createContextMenu() {
        if (this.contextMenu) return; // Évite de recréer le menu s'il existe déjà

        // Créer le menu contextuel
        this.contextMenu = document.createElement('div');
        this.contextMenu.id = 'iris-context-menu';

        // Ajouter les options au menu
        const spyItem = document.createElement('div');
        spyItem.textContent = 'Spy this element';
        spyItem.addEventListener('click', () => this.doSpyElement());
        spyItem.className = 'iris-context-menu-action';
        const spyItemText = document.createElement('span');
        const spyItemShortcut = document.createElement('span'); 
        spyItemShortcut.className = 'iris-context-menu-action-shortcut';
        spyItemShortcut.textContent = 'Enter';
        spyItem.appendChild(spyItemText);
        spyItem.appendChild(spyItemShortcut);
        this.contextMenu.appendChild(spyItem);

        // Add Advanced Spy option
        const advancedSpyItem = document.createElement('div');
        advancedSpyItem.className = 'iris-context-menu-action';
        const advancedSpyItemText = document.createElement('span');
        const advancedSpyItemShortcut = document.createElement('span');
        advancedSpyItemText.textContent = 'Advanced spy'; 
        advancedSpyItemShortcut.className = 'iris-context-menu-action-shortcut';
        advancedSpyItemShortcut.textContent = 'Alt+Enter';
        advancedSpyItem.appendChild(advancedSpyItemText);
        advancedSpyItem.appendChild(advancedSpyItemShortcut);
        advancedSpyItem.addEventListener('click', () => this.doAdvancedSpyElement());

        
        
        const cancelItem = document.createElement('div');
        cancelItem.className = 'iris-context-menu-action';
        const cancelItemText = document.createElement('span');
        const cancelItemShortcut = document.createElement('span');
        cancelItemText.textContent = 'Cancel spy mode';
        cancelItemShortcut.className = 'iris-context-menu-action-shortcut';
        cancelItemShortcut.textContent = 'Escape';
        cancelItem.appendChild(cancelItemText);
        cancelItem.appendChild(cancelItemShortcut);
        cancelItem.addEventListener('click', () => this.doCancel());
        
        // Ajouter les items au menu
        this.contextMenu.appendChild(spyItem);
        this.contextMenu.appendChild(advancedSpyItem);
        this.contextMenu.appendChild(cancelItem);
    }

    // Méthode pour ajouter les styles
    addStyles() {
        if (this.style) return; // Évite de recréer les styles s'ils existent déjà

        this.style = document.createElement('style');
        this.style.innerHTML = `
            /* Style pour le menu contextuel */
            #iris-context-menu {
                position: absolute;
                display: none;
                background: white;
                box-shadow: 0 4px 9px #DEE4FF;
                border-radius: 10px;
                padding: 7px 10px;
                z-index: 1000;
                font-family: Roboto, sans-serif;
                font-size: 14px;
                color: #303167;
                min-width: 290px;
                overflow: hidden;
                z-index: 99999999;
            }

            /* Style pour chaque option */
            #iris-context-menu div {
                font-size: 15px;
                font-weight: 500;
                padding: 2px 10px;
                cursor: pointer;
                color: #303167;
                transition: background-color 0.3s ease;
                border-radius: 3px;
            }
            
            .iris-context-menu-action {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .iris-context-menu-action .iris-context-menu-action-shortcut {
                opacity: 0.7;
            }

            /* Effet au survol des options */
            #iris-context-menu div:hover {
                background-color: #EDF1FF; /* Couleur bleu vif pour le survol */
            }

            /* Style option sélectionnée */
            #iris-context-menu div:active {
                background-color: #EDF1FF; /* Teinte légèrement plus sombre pour le clic */
            }
        `;
    }

    // Ajouter le menu et les styles dans la page
    addMenuToPage() {
        this.createContextMenu(); // S'assure que le menu est prêt
        this.addStyles(); // S'assure que les styles sont prêts

        if (!document.body.contains(this.contextMenu)) {
            document.body.appendChild(this.contextMenu); // Injecte le menu dans la page
        }

        if (!document.head.contains(this.style)) {
            document.head.appendChild(this.style); // Injecte les styles dans la page
        }

        this.addEventListeners();
    }

    // Supprimer le menu et les styles de la page
    deleteMenuFromPage() {
        if (this.contextMenu && document.body.contains(this.contextMenu)) {
            document.body.removeChild(this.contextMenu); // Retire le menu du DOM
        }

        if (this.style && document.head.contains(this.style)) {
            document.head.removeChild(this.style); // Retire les styles du DOM
        }

        this.removeEventListeners();
        this.contextMenu = null;
        this.style = null;
    }

    // Ajouter les écouteurs d'événements
    addEventListeners() {
        if (!this.boundContextMenuHandler) {
            this.boundContextMenuHandler = this.handleContextMenu.bind(this);
            this.boundClickHandler = this.hideContextMenu.bind(this);
        }

        document.addEventListener('contextmenu', this.boundContextMenuHandler);
        document.addEventListener('click', this.boundClickHandler);
    }

    // Supprimer les écouteurs d'événements
    removeEventListeners() {
        if (this.boundContextMenuHandler) {
            document.removeEventListener('contextmenu', this.boundContextMenuHandler);
            document.removeEventListener('click', this.boundClickHandler);
        }
    }

    // Gérer le clic droit pour afficher le menu
    handleContextMenu(event) {
        event.preventDefault(); // Empêche l'ouverture du menu contextuel natif
        if (this.contextMenu) {
            this.showContextMenu(event.pageX, event.pageY);
        }
    }

    // Afficher le menu à une position donnée
    showContextMenu(x, y) {
        if (this.contextMenu) {
            const menuWidth = this.contextMenu.offsetWidth || 340;
            const viewportWidth = window.innerWidth;

            if (x + menuWidth + 25 >= viewportWidth) {
                x -= (x + menuWidth - viewportWidth);
            }

            this.contextMenu.style.display = 'block';
            this.contextMenu.style.left = `${x}px`;
            this.contextMenu.style.top = `${y}px`;
        }
    }

    // Cacher le menu
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.style.display = 'none';
        }
    }

    // Actions des items
    doSpyElement() {
        this.hideContextMenu();
        if( this.spyAndHighlightElement){
            this.spyAndHighlightElement.doSpyElement();
        }
    }

    doAdvancedSpyElement() {
        this.hideContextMenu();
        if( this.spyAndHighlightElement){
            this.spyAndHighlightElement.doAdvancedSpy();
        }
    }

    doCancel() {
        this.hideContextMenu();
        if( this.spyAndHighlightElement){
            this.spyAndHighlightElement.doCancelSpy();
        }
    }

    // Vérifier si un élément spécifique fait partie du menu pour eviter de le spyer
    existInMenu(anElementHTML) {
        if (this.contextMenu) {
            // Vérifie si l'élément passé en argument est le menu lui-même
            if (this.contextMenu.isEqualNode(anElementHTML)) {
                return true;
            }
    
            // Sinon, vérifie si l'élément est un enfant du menu
            const elements = Array.from(this.contextMenu.children);
            // verification récursif des sous-éléments du menu
            elements.forEach((el) => {
                if (el.children.length > 0) {
                    const subElements = Array.from(el.children)
                    elements.push(...subElements);
                }
            });
            return elements.some(el => el.isEqualNode(anElementHTML));
        }
        return false;
    }
}
